{"permissions": {"allow": ["mcp__supabase__list_projects", "mcp__supabase__apply_migration", "Bash(git add:*)", "Bash(git commit:*)", "mcp__supabase__generate_typescript_types", "Bash(npm run lint)", "Bash(npx next lint:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "mcp__playwright__browser_navigate", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__browsertools__takeScreenshot", "mcp__playwright__browser_click", "mcp__playwright__browser_tab_new", "mcp__playwright__browser_wait_for", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)", "Bash(npm run dev:*)", "mcp__browsermcp__browser_navigate", "mcp__browsermcp__browser_click", "mcp__browsermcp__browser_get_console_logs", "mcp__browsertools__getNetworkErrors", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_type", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_network_requests", "Bash(ls:*)", "<PERSON><PERSON>(pkill:*)", "Bash(PORT=3005 npm run dev)", "mcp__browsertools__getConsoleLogs", "mcp__browsermcp__browser_screenshot", "mcp__browsertools__getNetworkLogs", "mcp__supabase__list_tables", "mcp__playwright__browser_press_key", "mcp__playwright__browser_take_screenshot", "mcp__browsermcp__browser_wait", "mcp__browsermcp__browser_snapshot", "mcp__browsermcp__browser_type", "mcp__browsermcp__browser_press_key", "mcp__browsermcp__browser_select_option", "Bash(npx supabase migration:*)", "mcp__supabase__execute_sql", "WebFetch(domain:docs.polar.sh)", "mcp__firecrawl-mcp__firecrawl_search", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "<PERSON><PERSON>(true)", "mcp__playwright__browser_snapshot", "Bash(npx supabase:*)", "mcp__playwright__browser_tab_list", "mcp__playwright__browser_tab_select", "mcp__puppeteer__puppeteer_evaluate", "Bash(cp:*)", "Bash(grep:*)", "mcp__brave-search__brave_web_search", "mcp__firecrawl-mcp__firecrawl_scrape", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(find:*)"], "deny": []}}