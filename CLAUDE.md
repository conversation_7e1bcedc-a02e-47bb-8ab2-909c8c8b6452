# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Stage Manager Pro is a Next.js 14+ application for event timing and presentation management, designed to compete with StageTimer.io. The platform provides customizable countdown/countup timers, AI-powered speech parsing, subtitle display, and collaborative features.

## Development Commands

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run linting
npm run lint
```

## Architecture Overview

### Tech Stack
- **Framework**: Next.js 14 with App Router
- **Authentication**: Supabase Auth with email/password and social logins
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Styling**: Tailwind CSS with dark theme
- **UI Components**: Radix UI components (@radix-ui)
- **State Management**: Zustand
- **Type Safety**: TypeScript with strict mode
- **Animations**: Framer Motion

### Project Structure
- `/src/app/` - Next.js App Router pages and API routes
  - `(app)/` - Protected app routes (dashboard)
  - `(auth)/` - Authentication routes (login, signup)
  - `auth/` - Auth callback and verification routes
- `/src/components/` - Reusable UI components
- `/src/lib/` - Utilities and third-party integrations
  - `supabase/` - Supabase client configurations

### Key Patterns

1. **Authentication Flow**
   - Middleware (`src/middleware.ts`) handles session updates
   - Server-side Supabase client (`src/lib/supabase/server.ts`) for API routes
   - Client-side Supabase client (`src/lib/supabase/client.ts`) for browser
   - Auth callback route handles OAuth redirects

2. **Routing Structure**
   - Route groups for layout separation: `(app)` for authenticated, `(auth)` for public
   - Protected routes require authentication via middleware
   - API routes in `app/auth/` for auth callbacks

3. **Component Architecture**
   - UI components in `src/components/ui/` using class-variance-authority
   - Tailwind utility classes with `cn()` helper for conditional styling
   - Dark theme by default with CSS variables

4. **Environment Variables Required**
   ```
   NEXT_PUBLIC_SUPABASE_URL=
   NEXT_PUBLIC_SUPABASE_ANON_KEY=
   ```

## Important Considerations

- Always check authentication state before accessing protected resources
- Use server components for initial data fetching
- Client components should handle real-time updates via Supabase subscriptions
- Follow the existing dark theme design system with green (#00DC82) and pink (#FF0080) accents
- Maintain TypeScript strict mode compliance
- Use path alias `@/` for imports from `src/`