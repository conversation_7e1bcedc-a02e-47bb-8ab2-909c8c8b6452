# Stage Manager Pro - Product Requirements Document

## 1. Executive Summary

Stage Manager Pro is a modern SaaS platform for event timing and presentation management that competes directly with StageTimer.io. The platform provides customizable countdown/countup timers, AI-powered speech parsing, subtitle display, and collaborative features for event organizers, speakers, and production teams.

### Key Differentiators
- AI-powered natural language input for creating complex timing sequences
- Built-in subtitle/teleprompter functionality
- Modern dark theme UI optimized for stage visibility
- Competitive pricing at $5/month
- Ad-supported free tier for basic features

## 2. Market Analysis

### Target Market
- Event organizers and producers
- Conference speakers and presenters
- YouTube content creators (tutorials, reviews, vlogs)
- Podcast hosts (interview shows, solo episodes, panel discussions)
- Webinar hosts
- Church service coordinators
- Educational institutions
- Live streaming professionals
- Corporate meeting facilitators

### Competitor Analysis
**StageTimer.io** (Primary Competitor)
- Revenue: ~$10k/month
- Strengths: Established brand, API integrations, offline desktop apps
- Weaknesses: Dated UI, no AI features, no built-in subtitles/teleprompter

## 3. Product Vision & Goals

### Vision
To become the most intuitive and feature-rich stage timing solution that empowers speakers and event organizers to deliver flawless presentations.

### Goals
- Capture 20% of StageTimer's market share within 12 months
- Achieve $2k MRR within 6 months
- Build a user base of 1,000 active subscribers by end of Year 1

## 4. User Personas

### 1. Event Producer Emma
- Manages multiple speakers at conferences
- Needs reliable timing and communication tools
- Values professional appearance and ease of use

### 2. Content Creator Carlos
- Records YouTube videos with structured segments
- Plans videos minute-by-minute (e.g., "0-2min: Hook, 2-5min: Problem, 5-15min: Tutorial")
- Wants AI assistance for script timing
- Needs teleprompter functionality
- Tracks segment completion during recording

### 3. Corporate Presenter Patricia
- Gives regular presentations and webinars
- Requires simple, reliable countdown timers
- Values sharing and collaboration features

### 4. Podcast Host Pablo
- Records 1-hour structured podcasts
- Needs segment timing (e.g., "0-5min: Intro, 5-15min: Guest background, 15-45min: Main topic, 45-55min: Q&A, 55-60min: Wrap-up")
- Wants visual cues for topic transitions
- Tracks time per discussion topic
- Needs notes/talking points display

## 5. Core Features

### 5.1 Timer Management
- **Countdown Timer**: Traditional countdown from set duration
- **Countup Timer**: Elapsed time tracking
- **Clock Display**: Show current time
- **Multiple Timer Formats**: 
  - MM:SS
  - HH:MM:SS
  - Custom formats (12h/24h)

### 5.2 AI-Powered Speech Parser
- **Natural Language Input**: 
  - Event example: "Person A speaks for 10 minutes about introduction, then Person B for 25 minutes on main topic"
  - YouTube example: "2 minute hook, 3 minute problem explanation, 10 minute tutorial, 2 minute call-to-action"
  - Podcast example: "5 min intro music and sponsors, 10 min guest introduction, 40 min interview, 5 min closing"
- **DeepSeek AI Integration**: Parse and structure timing sequences
- **Template Suggestions**: 
  - YouTube video templates (Tutorial, Review, Vlog, etc.)
  - Podcast templates (Interview, Solo, Panel, etc.)
  - Event templates (Conference, Webinar, Workshop, etc.)
- **Script Import**: Support for importing existing scripts/rundowns

### 5.3 Subtitle/Teleprompter System
- **Real-time Subtitles**: Display speaker notes/script
- **Adjustable Font Size**: Dynamic sizing for visibility
- **Scroll Speed Control**: Manual and automatic scrolling
- **Cue Points**: Mark important transitions

### 5.4 Presentation Modes
- **Speaker View**: Full-screen timer with subtitles
- **Controller View**: Admin panel for managing timers
- **Audience View**: Public-facing countdown (optional)
- **Stage Display**: High-contrast mode for stage monitors

### 5.5 Collaboration & Sharing
- **Shareable Links**: Unique URLs for each timer session
- **Role-Based Access**:
  - Creator: Full edit access
  - Admin: Edit access (assigned by creator)
  - Viewer: Read-only access
- **Real-time Sync**: All connected devices update simultaneously
- **QR Code Generation**: Quick sharing for events

### 5.6 User Management
- **Supabase Authentication**:
  - Email/password login
  - Social logins (Google, GitHub)
  - Magic link authentication
- **User Profiles**: Save preferences and timer templates
- **Team Workspaces**: Organize timers by event/project

### 5.7 Data Persistence
- **Speech/Timer Storage**: Save all configurations in Supabase
- **Version History**: Track changes to timer sequences
- **Templates Library**: Save and reuse common timer setups
- **Export Options**: CSV, JSON for backup/integration

## 6. Technical Architecture

### 6.1 Frontend
- **Framework**: Next.js 14+ (App Router)
- **Styling**: Tailwind CSS with custom dark theme
- **State Management**: Zustand
- **Real-time**: Supabase Realtime for live updates
- **UI Components**: 
  - Radix UI for accessibility (@radix-ui/react-dialog, @radix-ui/react-dropdown-menu, @radix-ui/react-slot)
  - Framer Motion for animations
  - Class Variance Authority for component variants
  - Lucide React for icons

### 6.2 Backend
- **Database**: Supabase (PostgreSQL)
  - Tables: users, speeches, timers, shares, templates
  - Row Level Security (RLS) for access control
- **Authentication**: Supabase Auth with SSR support
  - Email/password authentication
  - Social logins (Google, GitHub)
  - Magic link authentication
  - Session management via middleware
- **AI Integration**: DeepSeek API for text parsing
- **File Storage**: Supabase Storage for exports/imports

### 6.3 Payments & Monetization
- **Polar.sh Integration**:
  - Subscription management ($5/month)
  - Usage-based billing for API access
  - Webhook endpoint: `/api/webhook/polar`
  - Handles subscription events (created, updated, cancelled)
  - Verifies webhook signatures for security
- **Ad Integration**: 
  - Google AdSense or similar
  - Display ads in free tier (fullscreen mode)
  - Ad-free for paid subscribers

### 6.4 Deployment
- **Hosting**: Vercel (optimized for Next.js)
- **CDN**: Vercel Edge Network
- **Monitoring**: Vercel Analytics + Sentry
- **API Rate Limiting**: Upstash Redis
- **Environment Variables**:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
  - `POLAR_WEBHOOK_SECRET` (for webhook verification)
  - Additional Polar.sh API keys as needed

## 7. Design Specifications

### 7.1 Theme & Branding
- **Primary Colors**: 
  - Background: #000000 (Pure black)
  - Surface: #0A0A0A (Near black)
  - Primary: #00DC82 (Vibrant green)
  - Accent: #FF0080 (Hot pink)
- **Typography**:
  - Headers: Inter (loaded via Next.js font optimization)
  - Body: Inter (loaded via Next.js font optimization)
  - Monospace: JetBrains Mono (for timers, loaded via Next.js font optimization)

### 7.2 Key UI Components
- **Timer Display**: Large, high-contrast digits
- **Progress Rings**: Visual countdown indicators
- **Gradient Effects**: Subtle gradients for depth
- **Glass Morphism**: For overlay panels
- **Smooth Transitions**: 60fps animations

### 7.3 Responsive Design
- Mobile-first approach
- Breakpoints: Mobile (< 640px), Tablet (640-1024px), Desktop (> 1024px)
- Touch-optimized controls for mobile

## 8. Feature Roadmap

### Phase 1: MVP (Months 1-2)
- Basic timer functionality (countdown/countup)
- User authentication (Supabase Auth with email/password and social logins)
- Simple speech creation and management
- Basic sharing functionality
- Dark theme UI with green/pink accent colors
- Route-based access control with middleware

### Phase 2: AI Integration (Months 2-3)
- DeepSeek AI integration
- Natural language timer creation
- Template library
- Subtitle display system

### Phase 3: Monetization (Months 3-4)
- Polar.sh payment integration
  - Webhook handler at `/api/webhook/polar`
  - Subscription lifecycle management
  - Payment verification
- Free tier with ads
- Premium features lock
- Usage analytics

### Phase 4: Advanced Features (Months 4-6)
- API for third-party integrations
- Webhook support
- Advanced scheduling
- Multi-language support
- Mobile apps (React Native)
- Offline mode (PWA)

### Phase 5: Scale & Optimize (Months 6-12)
- Performance optimizations
- Advanced analytics dashboard
- Team collaboration features
- White-label options
- Integration marketplace

## 9. Monetization Strategy

### Pricing Tiers

#### Free Tier
- 3 active timers/month
- Basic countdown/countup
- 2 connected devices
- Ads in fullscreen mode
- Community support

#### Pro Tier ($5/month)
- Unlimited timers
- AI-powered timer creation
- Unlimited connected devices
- No ads
- Subtitle/teleprompter
- Priority support
- API access (limited)
- Custom branding

#### Team Tier ($15/month)
- Everything in Pro
- 5 team members
- Advanced permissions
- Unlimited API access
- White-label options
- Dedicated support

## 10. Success Metrics

### Key Performance Indicators (KPIs)
- Monthly Recurring Revenue (MRR)
- Daily Active Users (DAU)
- Conversion rate (Free to Paid)
- User retention rate
- Average session duration
- Number of timers created
- Share rate (viral coefficient)

### Target Metrics (Year 1)
- 10,000 registered users
- 1,000 paying subscribers
- $5,000 MRR
- 40% monthly retention rate
- 10% free-to-paid conversion

## 11. Risk Analysis

### Technical Risks
- **Real-time Sync Issues**: Mitigate with robust error handling and fallbacks
- **AI API Costs**: Implement caching and rate limiting
- **Scalability**: Design for horizontal scaling from day one

### Business Risks
- **Competition Response**: StageTimer might add similar features
- **Payment Processing**: Have backup payment providers
- **User Acquisition Cost**: Focus on organic growth and SEO

### Mitigation Strategies
- Build unique features competitors can't easily replicate
- Focus on superior UX and modern design
- Create strong content marketing strategy
- Build community and user loyalty

## 12. Development Guidelines

### Code Quality
- TypeScript with strict mode enabled
- ESLint with Next.js configuration
- Path aliases using @/ for src directory imports
- App Router conventions for file-based routing
- Server Components by default, Client Components when needed

### Security
- Input validation and sanitization
- Rate limiting on all endpoints
- Regular security audits
- GDPR compliance
- SOC 2 preparation

### Performance
- Target 90+ Lighthouse score
- Sub-3s page load times
- Optimistic UI updates
- Edge caching for static assets

## 13. Marketing Strategy

### Launch Strategy
- Beta launch with ProductHunt
- Content marketing (blog posts, tutorials)
- SEO optimization for timer-related keywords
- Social media presence (Twitter, LinkedIn)
- Influencer partnerships with event organizers

### Growth Tactics
- Referral program (1 month free for each referral)
- Free tier to drive adoption
- Integration partnerships
- Educational content and webinars
- Community building (Discord/Slack)

## 14. Support & Documentation

### User Support
- Comprehensive documentation site
- Video tutorials
- In-app tooltips and onboarding
- Email support for paid users
- Community forum

### Developer Resources
- API documentation
- Webhook examples
- Integration guides
- Open-source plugins

## 15. Legal & Compliance

### Requirements
- Terms of Service
- Privacy Policy
- Cookie Policy
- GDPR compliance
- CCPA compliance
- Accessibility (WCAG 2.1 AA)

## 16. Conclusion

Stage Manager Pro aims to disrupt the event timing market by combining modern design, AI capabilities, and competitive pricing. By focusing on user experience and innovative features like AI-powered timer creation and built-in subtitles, we can capture significant market share from established competitors while building a sustainable SaaS business.

### Next Steps
1. Validate core assumptions with user interviews
2. Create detailed wireframes and mockups
3. Set up development environment
4. Begin MVP development with core timer functionality
5. Establish beta testing program

## 17. Implementation Plan

### Phase 1: Database & Auth Setup (Week 1)
1. **Supabase Database Schema**
   - Users table (extends Supabase auth.users)
   - Speeches/Sessions table (title, description, created_by, settings)
   - Segments table (speech_id, title, duration, order, notes, segment_type)
   - Shares table (speech_id, share_token, permissions, expires_at)
   - Templates table (name, segments, category, is_public)

2. **Authentication Flow**
   - Implement email/password signup with verification
   - Add social logins (Google, GitHub)
   - Create protected routes for dashboard
   - Setup user profile management

### Phase 2: Core Timer UI (Week 2)
1. **Timer Display Component**
   - Large countdown/countup display
   - Progress ring visualization
   - Segment indicator
   - Play/pause/reset controls

2. **Session Builder**
   - Create new session/speech
   - Add/edit/reorder segments
   - Set duration for each segment
   - Add notes/talking points

3. **Templates**
   - YouTube templates (Tutorial, Review, Vlog)
   - Podcast templates (30min, 60min formats)
   - Event templates

### Phase 3: AI Integration (Week 3)
1. **Natural Language Parser**
   - Integrate DeepSeek API
   - Parse natural language to segments
   - Handle various input formats
   - Error handling and validation

2. **Smart Suggestions**
   - Suggest segment durations
   - Recommend segment types
   - Auto-generate segment titles

### Phase 4: Real-time Features (Week 4)
1. **Live Sync**
   - Implement Supabase Realtime
   - Sync timer state across devices
   - Handle connection drops

2. **Sharing System**
   - Generate shareable links
   - QR code generation
   - Permission levels (view/control)

### Phase 5: Monetization (Week 5-6)
1. **Polar Integration**
   - Setup webhook handler
   - Implement subscription tiers
   - Feature gating
   - Usage tracking

2. **Free Tier Limitations**
   - Implement ad system
   - Limit active sessions
   - Restrict sharing features

### MVP Launch Checklist
- [ ] Database schema created and tested
- [ ] Authentication flow complete
- [ ] Basic timer functionality working
- [ ] AI parsing integrated
- [ ] Sharing system functional
- [ ] Polar payments integrated
- [ ] Production deployment on Vercel
- [ ] Analytics and monitoring setup

---

*Document Version: 1.1*  
*Last Updated: [Current Date]*  
*Author: [Your Name]* 