{"name": "stage-manager-pro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@polar-sh/sdk": "^0.34.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-slot": "^1.0.2", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.3", "@tabler/icons-react": "^3.34.0", "@use-gesture/react": "^10.3.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^10.18.0", "lucide-react": "^0.309.0", "nanoid": "^5.1.5", "next": "14.0.4", "react": "^18", "react-dom": "^18", "react-intersection-observer": "^9.16.0", "react-spring": "^10.0.1", "tailwind-merge": "^2.2.0", "three": "^0.177.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}