"use client"

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { LucideIcon } from 'lucide-react'

interface FeatureCardProps {
  title: string
  description: string
  icon: LucideIcon
  gradient: string
  delay?: number
  interactive?: boolean
}

export default function FeatureCard({ 
  title, 
  description, 
  icon: Icon, 
  gradient,
  delay = 0,
  interactive = true
}: FeatureCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isPressed, setIsPressed] = useState(false)
  const { ref, inView } = useInView({
    threshold: 0.2,
    triggerOnce: true
  })

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50, rotateX: 15 }}
      animate={inView ? { 
        opacity: 1, 
        y: 0, 
        rotateX: 0,
        transition: {
          duration: 0.6,
          delay,
          ease: [0.215, 0.61, 0.355, 1]
        }
      } : {}}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onTapStart={() => setIsPressed(true)}
      onTap={() => setIsPressed(false)}
      onTapCancel={() => setIsPressed(false)}
      className="relative group perspective-1000"
    >
      {/* Glow Effect */}
      <motion.div
        className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${gradient} opacity-0 blur-xl transition-opacity duration-500`}
        animate={{ 
          opacity: isHovered ? 0.3 : 0,
          scale: isHovered ? 1.05 : 1
        }}
      />

      {/* Main Card */}
      <motion.div
        className="relative backdrop-blur-xl bg-white/5 rounded-3xl p-8 border border-white/10 overflow-hidden"
        animate={{
          scale: isPressed ? 0.95 : isHovered ? 1.02 : 1,
          rotateY: isHovered ? (interactive ? 5 : 0) : 0,
          z: isHovered ? 50 : 0
        }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        style={{
          transformStyle: "preserve-3d"
        }}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent" />
          <svg className="absolute bottom-0 right-0 w-32 h-32" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="40" fill="none" stroke="currentColor" strokeWidth="1" opacity="0.1" />
            <circle cx="50" cy="50" r="25" fill="none" stroke="currentColor" strokeWidth="1" opacity="0.1" />
            <circle cx="50" cy="50" r="10" fill="none" stroke="currentColor" strokeWidth="1" opacity="0.1" />
          </svg>
        </div>

        {/* Icon Container */}
        <motion.div
          className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${gradient} mb-6`}
          animate={{
            rotateX: isHovered ? 15 : 0,
            rotateY: isHovered ? -15 : 0,
            scale: isHovered ? 1.1 : 1
          }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30
          }}
          style={{
            transformStyle: "preserve-3d"
          }}
        >
          <motion.div
            animate={{
              rotateZ: isHovered ? 10 : 0
            }}
            transition={{ duration: 0.3 }}
          >
            <Icon className="w-8 h-8 text-white" />
          </motion.div>
        </motion.div>

        {/* Content */}
        <motion.div
          animate={{
            x: isHovered ? 5 : 0
          }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="text-2xl font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-gray-300 group-hover:bg-clip-text transition-all duration-300">
            {title}
          </h3>
          <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
            {description}
          </p>
        </motion.div>

        {/* Hover Indicator */}
        <motion.div
          className="absolute top-4 right-4 w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500"
          animate={{
            scale: isHovered ? 1 : 0,
            opacity: isHovered ? 1 : 0
          }}
          transition={{ duration: 0.2 }}
        />

        {/* Interactive Particles */}
        {isHovered && interactive && (
          <motion.div className="absolute inset-0 pointer-events-none">
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white rounded-full"
                initial={{
                  x: Math.random() * 100 + '%',
                  y: Math.random() * 100 + '%',
                  opacity: 0
                }}
                animate={{
                  y: [null, '-100%'],
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: 2,
                  delay: i * 0.2,
                  repeat: Infinity
                }}
              />
            ))}
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  )
}