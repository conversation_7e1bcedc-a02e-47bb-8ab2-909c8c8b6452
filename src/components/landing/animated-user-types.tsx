'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const userTypes = [
  { text: 'Everyone', gradient: 'from-green-500 to-emerald-500' },
  { text: 'Creators', gradient: 'from-purple-500 to-pink-500' },
  { text: 'Developers', gradient: 'from-blue-500 to-cyan-500' },
  { text: 'Speakers', gradient: 'from-red-500 to-orange-500' },
  { text: 'Teams', gradient: 'from-indigo-500 to-purple-500' },
  { text: 'Educators', gradient: 'from-yellow-500 to-orange-500' },
  { text: 'Podcasters', gradient: 'from-purple-500 to-pink-500' },
  { text: 'YouTubers', gradient: 'from-red-500 to-pink-500' },
  { text: 'Streamers', gradient: 'from-blue-500 to-purple-500' },
  { text: 'Coaches', gradient: 'from-green-500 to-teal-500' },
  { text: 'Presenters', gradient: 'from-orange-500 to-red-500' },
  { text: 'Event Hosts', gradient: 'from-pink-500 to-purple-500' },
  { text: 'Remote Teams', gradient: 'from-blue-500 to-indigo-500' },
  { text: 'Students', gradient: 'from-teal-500 to-green-500' },
  { text: 'Professionals', gradient: 'from-gray-500 to-gray-400' },
]

export default function AnimatedUserTypes() {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % userTypes.length)
    }, 2500) // Change every 2.5 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <span className="relative inline-block min-w-[200px]">
      <AnimatePresence mode="wait">
        <motion.span
          key={currentIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className={`bg-gradient-to-r ${userTypes[currentIndex].gradient} bg-clip-text text-transparent`}
        >
          {userTypes[currentIndex].text}
        </motion.span>
      </AnimatePresence>
    </span>
  )
}