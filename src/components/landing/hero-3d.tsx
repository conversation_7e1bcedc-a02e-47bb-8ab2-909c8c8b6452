"use client"

import { useRef, useState } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Float, Text3D, Center, useMatcapTexture, Environment } from '@react-three/drei'
import { motion } from 'framer-motion'
import * as THREE from 'three'

function FloatingTimer({ position, rotation }: { position: [number, number, number], rotation?: [number, number, number] }) {
  const meshRef = useRef<THREE.Mesh>(null)
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.1
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.4) * 0.2
    }
  })

  const [matcap] = useMatcapTexture('7B5254_E9DCC7_B19986_C8AC91')

  return (
    <Float
      speed={1.5}
      rotationIntensity={0.6}
      floatIntensity={0.6}
      floatingRange={[0, 0.5]}
    >
      <mesh
        ref={meshRef}
        position={position}
        rotation={rotation}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        scale={hovered ? 1.1 : 1}
      >
        <cylinderGeometry args={[0.8, 0.8, 0.2, 32]} />
        <meshMatcapMaterial matcap={matcap} color={hovered ? "#00DC82" : "#ffffff"} />
      </mesh>
    </Float>
  )
}

function TimerText() {
  const [matcap] = useMatcapTexture('7B5254_E9DCC7_B19986_C8AC91')
  
  return (
    <Center>
      <Text3D
        font="/fonts/Inter_Bold.json"
        size={0.8}
        height={0.2}
        curveSegments={12}
        bevelEnabled
        bevelThickness={0.02}
        bevelSize={0.02}
        bevelOffset={0}
        bevelSegments={5}
      >
        STAGE
        <meshMatcapMaterial matcap={matcap} color="#00DC82" />
      </Text3D>
    </Center>
  )
}

export default function Hero3D() {
  return (
    <div className="h-64 w-full relative">
      <Canvas
        camera={{ position: [0, 0, 8], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
        style={{ background: 'transparent' }}
      >
        <Environment preset="city" />
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#FF0080" />
        
        <FloatingTimer position={[-3, 0, 0]} rotation={[0.1, 0.2, 0]} />
        <FloatingTimer position={[3, 1, -1]} rotation={[-0.1, -0.2, 0.1]} />
        <FloatingTimer position={[0, -1.5, 1]} rotation={[0.2, 0, -0.1]} />
        
        <group position={[0, 0.5, 0]}>
          <TimerText />
        </group>
      </Canvas>
    </div>
  )
}