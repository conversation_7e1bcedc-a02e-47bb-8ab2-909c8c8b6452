'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Crown, Calendar, CreditCard, ExternalLink, AlertTriangle, Check, Star } from 'lucide-react'
import { useSubscription } from '@/hooks/useSubscription'
import { cn } from '@/lib/utils'

interface SubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function SubscriptionModal({ isOpen, onClose }: SubscriptionModalProps) {
  const { plan, subscriptions, loading, error, createCheckout, syncSubscription } = useSubscription()
  const [isUpgrading, setIsUpgrading] = useState(false)
  const [isSyncing, setIsSyncing] = useState(false)

  const activeSubscription = subscriptions.find(sub => sub.status === 'active')

  const handleUpgrade = async () => {
    try {
      setIsUpgrading(true)
      // Using the actual Polar.sh product ID from your account
      const proProductId = '8c1c7045-9830-42d2-928c-939ff50042df'
      const checkoutUrl = await createCheckout(proProductId)
      
      if (checkoutUrl) {
        window.open(checkoutUrl, '_blank')
      }
    } catch (error) {
      console.error('Error creating checkout:', error)
      alert('Failed to start upgrade process. Please try again.')
    } finally {
      setIsUpgrading(false)
    }
  }

  const handleSync = async () => {
    try {
      setIsSyncing(true)
      await syncSubscription()
    } catch (error) {
      console.error('Error syncing subscription:', error)
    } finally {
      setIsSyncing(false)
    }
  }


  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-gray-900 rounded-2xl border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <Crown className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Subscription Management</h2>
                  <p className="text-sm text-gray-400">Manage your TimedFlow plan</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-gray-400">Loading subscription details...</p>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                  <p className="text-red-400 mb-4">Error loading subscription details</p>
                  <button
                    onClick={handleSync}
                    disabled={isSyncing}
                    className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isSyncing ? 'Syncing...' : 'Retry'}
                  </button>
                </div>
              ) : (
                <>
                  {/* Current Plan Status */}
                  <div className="bg-gray-800/50 rounded-xl p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={cn(
                          "w-16 h-16 rounded-full flex items-center justify-center",
                          plan?.plan_type === 'free' 
                            ? "bg-gray-700" 
                            : "bg-gradient-to-r from-purple-500 to-pink-500"
                        )}>
                          {plan?.plan_type === 'free' ? (
                            <Crown className="w-8 h-8 text-gray-400" />
                          ) : (
                            <Crown className="w-8 h-8 text-white" />
                          )}
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-white mb-1">
                            {plan?.plan_type?.charAt(0).toUpperCase()}{plan?.plan_type?.slice(1)} Plan
                          </h3>
                          {plan?.plan_type === 'free' ? (
                            <p className="text-gray-400">Basic features with 5-minute timer limit</p>
                          ) : (
                            <p className="text-green-400 flex items-center">
                              <Check className="w-4 h-4 mr-1" />
                              Active subscription
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <button
                          onClick={handleSync}
                          disabled={isSyncing}
                          className="text-sm text-gray-400 hover:text-white transition-colors disabled:opacity-50"
                        >
                          {isSyncing ? 'Syncing...' : 'Sync Status'}
                        </button>
                      </div>
                    </div>

                    {/* Subscription Details for Pro Users */}
                    {activeSubscription && plan?.plan_type !== 'free' && (
                      <div className="mt-6 pt-6 border-t border-gray-700">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-xs font-medium text-gray-400 uppercase tracking-wide">Status</label>
                            <p className="text-white font-semibold capitalize">{activeSubscription.status}</p>
                          </div>
                          <div>
                            <label className="text-xs font-medium text-gray-400 uppercase tracking-wide">Next Billing</label>
                            <p className="text-white font-semibold">
                              {formatDate(activeSubscription.current_period_end)}
                            </p>
                          </div>
                          <div>
                            <label className="text-xs font-medium text-gray-400 uppercase tracking-wide">Started</label>
                            <p className="text-white font-semibold">
                              {formatDate(activeSubscription.started_at)}
                            </p>
                          </div>
                          <div>
                            <label className="text-xs font-medium text-gray-400 uppercase tracking-wide">Cancel at Period End</label>
                            <p className={cn(
                              "font-semibold",
                              activeSubscription.cancel_at_period_end ? "text-amber-400" : "text-green-400"
                            )}>
                              {activeSubscription.cancel_at_period_end ? "Yes" : "No"}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Plan Features */}
                  {plan?.plan_type === 'free' ? (
                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-white">Upgrade to Pro</h4>
                      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h5 className="font-semibold text-white mb-3 flex items-center">
                              <Star className="w-4 h-4 text-yellow-400 mr-2" />
                              Pro Plan Benefits
                            </h5>
                            <ul className="space-y-2 text-sm text-gray-300">
                              <li className="flex items-center">
                                <Check className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                                Unlimited timer duration
                              </li>
                              <li className="flex items-center">
                                <Check className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                                AI-powered speech parsing
                              </li>
                              <li className="flex items-center">
                                <Check className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                                Advanced collaboration features
                              </li>
                              <li className="flex items-center">
                                <Check className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                                Priority support
                              </li>
                              <li className="flex items-center">
                                <Check className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                                Custom branding options
                              </li>
                            </ul>
                          </div>
                          <div className="text-center">
                            <div className="text-3xl font-bold text-white mb-2">$10.00</div>
                            <div className="text-gray-400 mb-4">per month</div>
                            <button
                              onClick={handleUpgrade}
                              disabled={isUpgrading}
                              className="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold rounded-lg transition-all hover:scale-105 disabled:opacity-50 disabled:transform-none"
                            >
                              {isUpgrading ? 'Starting Upgrade...' : 'Upgrade Now'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-white">Your Pro Benefits</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-800/30 rounded-lg p-4">
                          <div className="flex items-center mb-2">
                            <Check className="w-5 h-5 text-green-400 mr-2" />
                            <span className="font-medium text-white">Unlimited Timers</span>
                          </div>
                          <p className="text-sm text-gray-400">No time limits on your presentations</p>
                        </div>
                        <div className="bg-gray-800/30 rounded-lg p-4">
                          <div className="flex items-center mb-2">
                            <Check className="w-5 h-5 text-green-400 mr-2" />
                            <span className="font-medium text-white">AI Features</span>
                          </div>
                          <p className="text-sm text-gray-400">Speech parsing and smart subtitles</p>
                        </div>
                        <div className="bg-gray-800/30 rounded-lg p-4">
                          <div className="flex items-center mb-2">
                            <Check className="w-5 h-5 text-green-400 mr-2" />
                            <span className="font-medium text-white">Collaboration</span>
                          </div>
                          <p className="text-sm text-gray-400">Advanced team features</p>
                        </div>
                        <div className="bg-gray-800/30 rounded-lg p-4">
                          <div className="flex items-center mb-2">
                            <Check className="w-5 h-5 text-green-400 mr-2" />
                            <span className="font-medium text-white">Priority Support</span>
                          </div>
                          <p className="text-sm text-gray-400">Get help when you need it</p>
                        </div>
                      </div>

                      {/* Manage Subscription Button */}
                      <div className="flex justify-center pt-4">
                        <a
                          href={`https://app.polar.sh/dashboard/${process.env.NEXT_PUBLIC_POLAR_ORGANIZATION_ID}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center space-x-2 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors"
                        >
                          <CreditCard className="w-4 h-4" />
                          <span>Manage Subscription</span>
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}