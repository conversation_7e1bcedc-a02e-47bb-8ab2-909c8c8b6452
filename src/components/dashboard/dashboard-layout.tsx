"use client"

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Clock, 
  LayoutGrid, 
  Zap, 
  Users, 
  Settings,
  LogOut,
  Menu,
  X,
  Sparkles,
  Timer,
  Mic,
  Video,
  Calendar,
  ChevronRight,
  Bell,
  Search,
  Moon,
  Sun
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSubscription } from '@/hooks/useSubscription'
import SubscriptionModal from '@/components/subscription/subscription-modal'

interface DashboardLayoutProps {
  children: React.ReactNode
}

const navItems = [
  { 
    icon: Timer, 
    label: 'Timers', 
    href: '/dashboard',
    gradient: 'from-green-500 to-emerald-500',
    description: 'Manage your active timers'
  },
  { 
    icon: LayoutGrid, 
    label: 'Templates', 
    href: '/dashboard/templates',
    gradient: 'from-blue-500 to-cyan-500',
    description: 'Pre-built timer templates'
  },
  { 
    icon: Mic, 
    label: 'Podcasts', 
    href: '/dashboard/podcasts',
    gradient: 'from-purple-500 to-pink-500',
    description: 'Podcast timer presets'
  },
  { 
    icon: Video, 
    label: 'YouTube', 
    href: '/dashboard/youtube',
    gradient: 'from-red-500 to-orange-500',
    description: 'Video content timers'
  }
]

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [hoveredItem, setHoveredItem] = useState<string | null>(
    null)
  const [isDarkMode, setIsDarkMode] = useState(true)
  const [time, setTime] = useState(new Date())
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false)
  const { plan, loading } = useSubscription()

  useEffect(() => {
    const timer = setInterval(() => setTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="min-h-screen bg-black overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-950 to-black" />
        <div className="absolute inset-0">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full mix-blend-screen filter blur-3xl opacity-20"
              style={{
                background: `radial-gradient(circle, ${i === 0 ? '#00DC82' : i === 1 ? '#FF0080' : '#00B4D8'} 0%, transparent 70%)`,
              }}
              animate={{
                x: [0, 100, 0],
                y: [0, -100, 0],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 20 + i * 5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              initial={{
                width: `${600 + i * 100}px`,
                height: `${600 + i * 100}px`,
                left: `${i * 30}%`,
                top: `${i * 20}%`,
              }}
            />
          ))}
        </div>
      </div>

      {/* Sidebar */}
      <motion.aside
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        className={cn(
          "fixed left-0 top-0 h-full z-40 transition-all duration-500",
          isCollapsed ? "w-20" : "w-72",
          "backdrop-blur-2xl bg-white/5 border-r border-white/10"
        )}
      >
        {/* Logo Section */}
        <div className="p-6 border-b border-white/10">
          <motion.div 
            className="flex items-center gap-3"
            whileHover={{ scale: 1.02 }}
          >
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl blur-lg opacity-50" />
              <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2.5 rounded-xl">
                <Clock className="w-6 h-6 text-black" />
              </div>
            </div>
            <AnimatePresence>
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                >
                  <h1 className="text-xl font-bold bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">
                    Stage Manager
                  </h1>
                  <p className="text-xs text-gray-500">Professional Edition</p>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* Live Clock */}
        {!isCollapsed && (
          <motion.div 
            className="px-6 py-4 border-b border-white/10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="text-center">
              <p className="text-3xl font-mono font-bold text-white">
                {time.toLocaleTimeString('en-US', { hour12: false })}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {time.toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  month: 'short', 
                  day: 'numeric' 
                })}
              </p>
            </div>
          </motion.div>
        )}

        {/* Navigation */}
        <nav className="p-4 space-y-2">
          {navItems.map((item, index) => {
            const isActive = pathname === item.href
            const Icon = item.icon

            return (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onHoverStart={() => setHoveredItem(item.label)}
                onHoverEnd={() => setHoveredItem(null)}
              >
                <Link href={item.href}>
                  <motion.div
                    className={cn(
                      "relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-300",
                      isActive 
                        ? "bg-white/10 text-white" 
                        : "text-gray-400 hover:text-white hover:bg-white/5"
                    )}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {/* Active Indicator */}
                    {isActive && (
                      <motion.div
                        className={cn("absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 rounded-full bg-gradient-to-b", item.gradient)}
                        layoutId="activeIndicator"
                      />
                    )}

                    {/* Icon with gradient on hover/active */}
                    <div className={cn(
                      "relative flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-300",
                      isActive || hoveredItem === item.label
                        ? "bg-gradient-to-br " + item.gradient
                        : "bg-white/5"
                    )}>
                      <Icon className={cn(
                        "w-5 h-5 transition-colors",
                        isActive || hoveredItem === item.label ? "text-white" : "text-gray-400"
                      )} />
                    </div>

                    {/* Label and Description */}
                    <AnimatePresence>
                      {!isCollapsed && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          className="flex-1"
                        >
                          <p className="font-medium">{item.label}</p>
                          <p className="text-xs text-gray-500">{item.description}</p>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Hover Arrow */}
                    {!isCollapsed && (isActive || hoveredItem === item.label) && (
                      <motion.div
                        initial={{ opacity: 0, x: -5 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -5 }}
                      >
                        <ChevronRight className="w-4 h-4" />
                      </motion.div>
                    )}
                  </motion.div>
                </Link>
              </motion.div>
            )
          })}
        </nav>

        {/* Bottom Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-white/10">
          {/* Combined User Profile & Upgrade Section */}
          <motion.div
            onClick={() => setShowSubscriptionModal(true)}
            className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 hover:bg-gradient-to-r hover:from-purple-500/30 hover:to-pink-500/30 transition cursor-pointer mb-4"
            whileHover={{ scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="relative">
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center text-black font-bold">
                U
              </div>
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-black" />
            </div>
            {!isCollapsed && (
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <Sparkles className="w-4 h-4 text-purple-400" />
                  <p className="text-sm font-medium text-white">
                    {loading ? 'Loading...' : plan ? plan.plan_type.charAt(0).toUpperCase() + plan.plan_type.slice(1) + ' Plan' : 'Free Plan'}
                  </p>
                </div>
                <p className="text-xs text-gray-400">
                  {plan?.plan_type === 'free' 
                    ? 'Upgrade to Pro for unlimited timers and AI features'
                    : 'Manage your subscription'
                  }
                </p>
              </div>
            )}
          </motion.div>

          {/* Settings & Logout */}
          <div className="flex gap-2 mt-3">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex-1 p-2 rounded-lg bg-white/5 hover:bg-white/10 transition"
            >
              <Settings className="w-4 h-4 text-gray-400 mx-auto" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex-1 p-2 rounded-lg bg-white/5 hover:bg-red-500/20 transition group"
            >
              <LogOut className="w-4 h-4 text-gray-400 group-hover:text-red-400 mx-auto" />
            </motion.button>
          </div>
        </div>

        {/* Collapse Button */}
        <motion.button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="absolute -right-4 top-8 w-8 h-8 rounded-full bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <motion.div
            animate={{ rotate: isCollapsed ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChevronRight className="w-4 h-4 text-white" />
          </motion.div>
        </motion.button>
      </motion.aside>

      {/* Main Content */}
      <motion.main
        className={cn(
          "relative z-10 transition-all duration-500",
          isCollapsed ? "ml-20" : "ml-72"
        )}
      >
        {/* Top Bar */}
        <motion.header
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="sticky top-0 z-30 backdrop-blur-2xl bg-black/50 border-b border-white/10"
        >
          <div className="flex items-center justify-between p-6">
            {/* Search Bar */}
            <div className="flex-1 max-w-2xl">
              
            </div>

            {/* Right Actions */}
            <div className="flex items-center gap-4 ml-6">
              {/* Theme Toggle */}
              {/* <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsDarkMode(!isDarkMode)}
                className="p-2.5 rounded-xl bg-white/5 hover:bg-white/10 transition"
              >
                {isDarkMode ? (
                  <Sun className="w-5 h-5 text-gray-400" />
                ) : (
                  <Moon className="w-5 h-5 text-gray-400" />
                )}
              </motion.button> */}

              {/* Notifications */}
              {/* <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative p-2.5 rounded-xl bg-white/5 hover:bg-white/10 transition"
              >
                <Bell className="w-5 h-5 text-gray-400" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              </motion.button> */}

              {/* Quick Actions */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2.5 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium flex items-center gap-2 hover:shadow-lg hover:shadow-green-500/25 transition"
              >
                <Zap className="w-4 h-4" />
                Quick Timer
              </motion.button>
            </div>
          </div>
        </motion.header>

        {/* Page Content */}
        <div className="p-6">
          {children}
        </div>
      </motion.main>

      {/* Mobile Menu Button */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="fixed bottom-6 right-6 z-50 lg:hidden w-14 h-14 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-lg"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isMobileMenuOpen ? (
          <X className="w-6 h-6 text-black" />
        ) : (
          <Menu className="w-6 h-6 text-black" />
        )}
      </motion.button>

      {/* Subscription Modal */}
      <SubscriptionModal 
        isOpen={showSubscriptionModal} 
        onClose={() => setShowSubscriptionModal(false)} 
      />
    </div>
  )
}