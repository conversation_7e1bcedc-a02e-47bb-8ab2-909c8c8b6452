'use client'

import { useState } from 'react'
import { <PERSON>, Pause, RotateCcw, Ski<PERSON><PERSON>or<PERSON> } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { Timer } from '@/types/database'
import { cn } from '@/lib/utils'

interface SessionControlsProps {
  timers: Timer[]
  currentTimer: Timer | null
  onTimerChange: (timerId: string) => void
  roomId: string
}

export default function SessionControls({ 
  timers, 
  currentTimer, 
  onTimerChange, 
  roomId 
}: SessionControlsProps) {
  const [isUpdating, setIsUpdating] = useState(false)
  const supabase = createClient()

  // Check if session is running (any timer is running)
  const isSessionRunning = timers.some(timer => timer.status === 'running')
  
  // Check if session is complete (all timers are finished)
  const isSessionComplete = timers.length > 0 && timers.every(timer => timer.status === 'finished')

  // Start the session (start first idle timer)
  const startSession = async () => {
    if (isUpdating) return
    
    setIsUpdating(true)
    try {
      // Find the first timer that's not finished
      const firstTimer = timers.find(timer => timer.status !== 'finished')
      
      if (firstTimer) {
        await supabase
          .from('timers')
          .update({
            status: 'running',
            started_at: new Date().toISOString()
          })
          .eq('id', firstTimer.id)
        
        onTimerChange(firstTimer.id)
        console.log('Session started with timer:', firstTimer.name)
      }
    } catch (error) {
      console.error('Error starting session:', error)
    }
    setIsUpdating(false)
  }

  // Pause the session (pause current running timer)
  const pauseSession = async () => {
    if (isUpdating || !currentTimer) return
    
    setIsUpdating(true)
    try {
      // Calculate elapsed time
      const startTime = new Date(currentTimer.started_at!).getTime()
      const now = Date.now()
      const sessionElapsed = Math.floor((now - startTime) / 1000)
      const totalElapsed = currentTimer.elapsed_time + sessionElapsed

      await supabase
        .from('timers')
        .update({
          status: 'paused',
          elapsed_time: Math.max(0, totalElapsed),
          started_at: null
        })
        .eq('id', currentTimer.id)
      
      console.log('Session paused')
    } catch (error) {
      console.error('Error pausing session:', error)
    }
    setIsUpdating(false)
  }

  // Reset the entire session
  const resetSession = async () => {
    if (isUpdating) return
    
    setIsUpdating(true)
    try {
      // Reset all timers to idle state
      await supabase
        .from('timers')
        .update({
          status: 'idle',
          elapsed_time: 0,
          started_at: null
        })
        .eq('room_id', roomId)
      
      console.log('Session reset')
    } catch (error) {
      console.error('Error resetting session:', error)
    }
    setIsUpdating(false)
  }

  // Skip to next timer
  const skipToNext = async () => {
    if (isUpdating || !currentTimer) return
    
    setIsUpdating(true)
    try {
      // Finish current timer
      await supabase
        .from('timers')
        .update({
          status: 'finished',
          started_at: null,
          elapsed_time: currentTimer.type === 'countdown' ? currentTimer.duration : currentTimer.elapsed_time
        })
        .eq('id', currentTimer.id)

      // Find next timer
      const currentIndex = timers.findIndex(t => t.id === currentTimer.id)
      if (currentIndex < timers.length - 1) {
        const nextTimer = timers[currentIndex + 1]
        
        // Start next timer
        await supabase
          .from('timers')
          .update({
            status: 'running',
            started_at: new Date().toISOString(),
            elapsed_time: 0
          })
          .eq('id', nextTimer.id)
        
        onTimerChange(nextTimer.id)
        console.log('Skipped to next timer:', nextTimer.name)
      }
    } catch (error) {
      console.error('Error skipping to next timer:', error)
    }
    setIsUpdating(false)
  }

  return (
    <div className="flex items-center justify-center space-x-6">
      {/* Reset Session */}
      <button
        onClick={resetSession}
        disabled={isUpdating || timers.every(t => t.status === 'idle')}
        className="p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all hover:scale-105 backdrop-blur-sm border border-gray-700/50"
        title="Reset Session"
      >
        <RotateCcw className="w-5 h-5" />
      </button>

      {/* Main Play/Pause Button */}
      {isSessionRunning ? (
        <button
          onClick={pauseSession}
          disabled={isUpdating}
          className="p-8 rounded-3xl bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black disabled:opacity-50 transition-all hover:scale-105 shadow-2xl"
          title="Pause Session"
        >
          <Pause className="w-12 h-12" />
        </button>
      ) : (
        <button
          onClick={startSession}
          disabled={isUpdating || isSessionComplete}
          className={cn(
            "p-8 rounded-3xl text-black disabled:opacity-50 disabled:cursor-not-allowed transition-all hover:scale-105 shadow-2xl",
            isSessionComplete 
              ? "bg-gradient-to-r from-gray-600 to-gray-700" 
              : "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
          )}
          title={isSessionComplete ? "Session Complete" : "Start Session"}
        >
          <Play className="w-12 h-12" />
        </button>
      )}

      {/* Skip to Next */}
      <button
        onClick={skipToNext}
        disabled={isUpdating || !currentTimer || currentTimer.status !== 'running'}
        className="p-4 rounded-xl bg-gray-800/50 hover:bg-gray-700/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all hover:scale-105 backdrop-blur-sm border border-gray-700/50"
        title="Skip to Next Timer"
      >
        <SkipForward className="w-5 h-5" />
      </button>
    </div>
  )
}
