'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Timer } from '@/types/database'
import { X, Clock, ArrowUp, Calendar, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AddTimerModalProps {
  isOpen: boolean
  onClose: () => void
  roomId: string
  existingTimers: Timer[]
}

export default function AddTimerModal({ isOpen, onClose, roomId, existingTimers }: AddTimerModalProps) {
  const supabase = createClient()
  const [isCreating, setIsCreating] = useState(false)
  const [durationInput, setDurationInput] = useState('5:00') // Separate state for input
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: 300, // 5 minutes default
    type: 'countdown' as 'countdown' | 'countup' | 'tod',
    trigger: (existingTimers.length > 0 ? 'linked' : 'manual') as 'manual' | 'linked' | 'scheduled',
    linkedToTimerId: '',
    scheduledStart: '',
    wrapUpTime: 60,
    overtimeAllowed: true
  })

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const parseDuration = (timeStr: string) => {
    const parts = timeStr.split(':')
    if (parts.length === 2) {
      const mins = parseInt(parts[0]) || 0
      const secs = parseInt(parts[1]) || 0
      return mins * 60 + secs
    }
    // Handle single number as minutes
    const singleNum = parseInt(timeStr)
    if (!isNaN(singleNum)) {
      return singleNum * 60
    }
    return 300 // default 5 minutes
  }

  // Handle duration input changes
  const handleDurationChange = (value: string) => {
    setDurationInput(value)
    const duration = parseDuration(value)
    setFormData(prev => ({ ...prev, duration }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)

    try {
      const nextPosition = Math.max(...existingTimers.map(t => t.position), -1) + 1
      
      const timerData = {
        room_id: roomId,
        name: formData.name || `Timer ${nextPosition + 1}`,
        description: formData.description || null,
        duration: formData.duration,
        type: formData.type,
        trigger: formData.trigger,
        linked_to_timer_id: formData.trigger === 'linked' ? formData.linkedToTimerId || null : null,
        scheduled_start: formData.trigger === 'scheduled' ? formData.scheduledStart || null : null,
        wrap_up_time: formData.wrapUpTime,
        overtime_allowed: formData.overtimeAllowed,
        position: nextPosition
      }

      const { error } = await supabase
        .from('timers')
        .insert(timerData)

      if (error) {
        console.error('Error creating timer:', error)
        alert(`Failed to create timer: ${error.message}`)
        return
      }

      // Reset form and close modal
      setFormData({
        name: '',
        description: '',
        duration: 300,
        type: 'countdown',
        trigger: (existingTimers.length >= 0 ? 'linked' : 'manual'), // Will be linked for the next timer
        linkedToTimerId: '',
        scheduledStart: '',
        wrapUpTime: 60,
        overtimeAllowed: true
      })
      setDurationInput('5:00')
      onClose()
    } catch (error) {
      console.error('Unexpected error:', error)
    } finally {
      setIsCreating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-gray-900 rounded-2xl border border-gray-800 p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold flex items-center space-x-2">
            <Plus className="w-5 h-5 text-green-500" />
            <span>Add Timer</span>
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-800 rounded-lg transition"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Timer Name */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Timer Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter timer name..."
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>

          {/* Timer Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="What happens during this segment? e.g., Introduction about X"
              rows={2}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
            />
          </div>

          {/* Timer Type */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Timer Type
            </label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: 'countdown', label: 'Countdown', icon: Clock },
                { value: 'countup', label: 'Count Up', icon: ArrowUp },
                { value: 'tod', label: 'Time of Day', icon: Calendar }
              ].map(({ value, label, icon: Icon }) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, type: value as any }))}
                  className={cn(
                    "p-3 rounded-lg border transition flex flex-col items-center space-y-1",
                    formData.type === value
                      ? "bg-green-500/20 border-green-500/50 text-green-400"
                      : "bg-gray-800 border-gray-700 hover:bg-gray-700"
                  )}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-xs">{label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Duration (only for countdown/countup) */}
          {formData.type !== 'tod' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Duration (MM:SS)
              </label>
              <input
                type="text"
                value={durationInput}
                onChange={(e) => handleDurationChange(e.target.value)}
                placeholder="1:00 or 1 (for 1 minute)"
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent font-mono"
              />
            </div>
          )}

          {/* Trigger Type - Auto-determined */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Start Trigger
            </label>
            <div className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-400">
              {existingTimers.length > 0 ? 'Auto-start after previous segment' : 'Manual start (first segment)'}
            </div>
          </div>


          {/* Advanced Settings */}
          {formData.type === 'countdown' && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Wrap-up Warning (seconds)
                </label>
                <input
                  type="number"
                  value={formData.wrapUpTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, wrapUpTime: parseInt(e.target.value) || 60 }))}
                  min="0"
                  max="300"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="overtime"
                  checked={formData.overtimeAllowed}
                  onChange={(e) => setFormData(prev => ({ ...prev, overtimeAllowed: e.target.checked }))}
                  className="w-4 h-4 text-green-500 bg-gray-800 border-gray-700 rounded focus:ring-green-500"
                />
                <label htmlFor="overtime" className="text-sm text-gray-300">
                  Allow overtime (negative countdown)
                </label>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-700 text-gray-300 rounded-lg hover:bg-gray-800 transition"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCreating}
              className="flex-1 px-4 py-2 bg-green-500 text-black font-medium rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition"
            >
              {isCreating ? 'Creating...' : 'Create Timer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
