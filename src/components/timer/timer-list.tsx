'use client'

import { useState } from 'react'
import { Timer } from '@/types/database'
import { createClient } from '@/lib/supabase/client'
import { Clock, Play, Pause, Link2, Calendar, Edit3, Check, X, Trash2, RotateCcw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TimerListProps {
  timers: Timer[]
  currentTimerId: string | null
  onSelectTimer: (timerId: string) => void
  roomId: string
}

export default function TimerList({ timers, currentTimerId, onSelectTimer, roomId }: TimerListProps) {
  const supabase = createClient()
  const [editingTimer, setEditingTimer] = useState<string | null>(null)
  const [editingName, setEditingName] = useState('')
  const [editingDescription, setEditingDescription] = useState('')
  const [editingDuration, setEditingDuration] = useState('')

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const parseDuration = (timeStr: string) => {
    const parts = timeStr.split(':')
    if (parts.length === 2) {
      const mins = parseInt(parts[0]) || 0
      const secs = parseInt(parts[1]) || 0
      return mins * 60 + secs
    }
    return 300 // default 5 minutes
  }

  const startEditing = (timer: Timer) => {
    setEditingTimer(timer.id)
    setEditingName(timer.name)
    setEditingDescription(timer.description || '')
    setEditingDuration(formatDuration(timer.duration))
  }

  const saveEditing = async () => {
    if (!editingTimer) return

    const { error } = await supabase
      .from('timers')
      .update({
        name: editingName,
        description: editingDescription || null,
        duration: parseDuration(editingDuration)
      })
      .eq('id', editingTimer)

    if (!error) {
      setEditingTimer(null)
      setEditingName('')
      setEditingDescription('')
      setEditingDuration('')
    }
  }

  const cancelEditing = () => {
    setEditingTimer(null)
    setEditingName('')
    setEditingDescription('')
    setEditingDuration('')
  }

  const deleteTimer = async (timerId: string) => {
    if (confirm('Are you sure you want to delete this timer?')) {
      const { error } = await supabase
        .from('timers')
        .delete()
        .eq('id', timerId)

      if (!error && editingTimer === timerId) {
        cancelEditing()
      }
    }
  }

  const toggleTimer = async (timer: Timer) => {
    try {
      if (timer.status === 'running') {
        // Pause timer
        const elapsed = timer.elapsed_time
        const { error } = await supabase
          .from('timers')
          .update({
            status: 'paused',
            elapsed_time: elapsed,
            started_at: null
          })
          .eq('id', timer.id)

        if (error) {
          console.error('Error pausing timer:', error)
        } else {
          console.log('Timer paused successfully:', timer.name)
        }
      } else {
        // Start timer
        const { error } = await supabase
          .from('timers')
          .update({
            status: 'running',
            started_at: new Date().toISOString()
          })
          .eq('id', timer.id)

        if (error) {
          console.error('Error starting timer:', error)
        } else {
          console.log('Timer started successfully:', timer.name)
        }
      }
    } catch (error) {
      console.error('Error toggling timer:', error)
    }
  }

  const resetTimer = async (timer: Timer) => {
    try {
      const { error } = await supabase
        .from('timers')
        .update({
          status: 'idle',
          elapsed_time: 0,
          started_at: null
        })
        .eq('id', timer.id)

      if (error) {
        console.error('Error resetting timer:', error)
      } else {
        console.log('Timer reset successfully:', timer.name)
      }
    } catch (error) {
      console.error('Error resetting timer:', error)
    }
  }

  const getTimerIcon = (timer: Timer) => {
    if (timer.trigger === 'linked') return <Link2 className="w-4 h-4 text-blue-400" />
    if (timer.trigger === 'scheduled') return <Calendar className="w-4 h-4 text-purple-400" />
    return <Clock className="w-4 h-4 text-gray-400" />
  }

  const getStatusColor = (status: Timer['status']) => {
    switch (status) {
      case 'running': return 'text-green-400'
      case 'paused': return 'text-yellow-400'
      case 'finished': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusBadge = (status: Timer['status']) => {
    switch (status) {
      case 'running': return 'bg-green-500/20 text-green-400 border-green-500/50'
      case 'paused': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50'
      case 'finished': return 'bg-red-500/20 text-red-400 border-red-500/50'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/50'
    }
  }

  const getTypeLabel = (type: Timer['type']) => {
    switch (type) {
      case 'countdown': return 'Countdown'
      case 'countup': return 'Count Up'
      case 'tod': return 'Time of Day'
      default: return 'Timer'
    }
  }

  return (
    <div className="space-y-4">
      {timers.map((timer, index) => (
        <div key={timer.id}>
          <div
            className={cn(
              'p-6 rounded-2xl transition-all border backdrop-blur-sm',
              currentTimerId === timer.id
                ? 'bg-green-500/10 border-green-500/50 shadow-xl ring-1 ring-green-500/20'
                : 'bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60 hover:border-gray-600/50'
            )}
          >
            {/* Header Row */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                  currentTimerId === timer.id
                    ? "bg-green-500 text-black"
                    : "bg-gray-700/70 text-gray-300"
                )}>
                  {index + 1}
                </div>

                <div className="flex items-center space-x-2">
                  {getTimerIcon(timer)}
                  <span className="text-xs text-gray-500 bg-gray-700/50 px-2 py-1 rounded-full">
                    {getTypeLabel(timer.type)}
                  </span>
                </div>
              </div>

              {/* Status Badge */}
              {timer.status !== 'idle' && (
                <div className={cn(
                  'px-3 py-1.5 rounded-full text-xs font-medium border flex items-center space-x-2',
                  getStatusBadge(timer.status)
                )}>
                  {timer.status === 'running' && <Play className="w-3 h-3" />}
                  {timer.status === 'paused' && <Pause className="w-3 h-3" />}
                  {timer.status === 'finished' && <Check className="w-3 h-3" />}
                  <span className="capitalize">{timer.status}</span>
                </div>
              )}
            </div>

            {/* Timer Name Row */}
            <div className="mb-4">
              {editingTimer === timer.id ? (
                <div className="space-y-2">
                  <input
                    type="text"
                    value={editingName}
                    onChange={(e) => setEditingName(e.target.value)}
                    className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    autoFocus
                  />
                  <textarea
                    value={editingDescription}
                    onChange={(e) => setEditingDescription(e.target.value)}
                    placeholder="Add description (optional)"
                    rows={2}
                    className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                  />
                </div>
              ) : (
                <div>
                  <button
                    onClick={() => onSelectTimer(timer.id)}
                    className="text-left w-full text-lg font-semibold hover:text-green-400 transition-colors"
                  >
                    {timer.name}
                  </button>
                  {timer.description && (
                    <p className="mt-1 text-sm text-gray-400">
                      {timer.description}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Duration and Controls Row */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {editingTimer === timer.id ? (
                  <input
                    type="text"
                    value={editingDuration}
                    onChange={(e) => setEditingDuration(e.target.value)}
                    placeholder="MM:SS"
                    className="bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-xl font-mono font-bold w-24 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                ) : (
                  <span className="text-2xl font-mono font-bold text-green-400">
                    {formatDuration(timer.duration)}
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                {editingTimer === timer.id ? (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={saveEditing}
                      className="p-2 text-green-400 hover:bg-green-500/20 rounded-lg transition-colors"
                      title="Save changes"
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    <button
                      onClick={cancelEditing}
                      className="p-2 text-gray-400 hover:bg-gray-500/20 rounded-lg transition-colors"
                      title="Cancel editing"
                    >
                      <X className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => deleteTimer(timer.id)}
                      className="p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors"
                      title="Delete timer"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    {/* Edit button only - session controls handle play/pause/reset */}
                    <button
                      onClick={() => startEditing(timer)}
                      className="p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg transition-colors"
                      title="Edit timer"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Auto-start indicator */}
          {timer.trigger === 'linked' && index > 0 && (
            <div className="ml-10 mt-3">
              <div className="flex items-center space-x-2 text-sm text-gray-500 bg-gray-800/30 px-3 py-2 rounded-lg border border-gray-700/30">
                <div className="w-1 h-4 bg-blue-400 rounded-full" />
                <span>Auto-start when timer {index} ends</span>
              </div>
            </div>
          )}
        </div>
      ))}

      {/* Empty State */}
      {timers.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-800/50 flex items-center justify-center">
            <Clock className="w-8 h-8 opacity-50" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No timers yet</h3>
          <p className="text-gray-400">Click &quot;Add Timer&quot; to create your first timer and start your session</p>
        </div>
      )}
    </div>
  )
}