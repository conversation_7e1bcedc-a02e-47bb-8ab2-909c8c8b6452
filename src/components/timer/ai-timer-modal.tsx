'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Spa<PERSON><PERSON>, Wand2, Clock } from 'lucide-react'

interface AITimerModalProps {
  isOpen: boolean
  onClose: () => void
  onCreate: (segments: Array<{ name: string; duration: number }>, name: string) => void
  isCreating?: boolean
}

export default function AITimerModal({ isOpen, onClose, onCreate, isCreating }: AITimerModalProps) {
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedSegments, setGeneratedSegments] = useState<Array<{ name: string; duration: number }>>([])
  const [timerName, setTimerName] = useState('AI Generated Timer')

  const generateSegments = async () => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    
    // Simulate AI processing (in a real app, this would call an AI API)
    setTimeout(() => {
      // Parse the prompt to generate segments
      const segments = parsePromptToSegments(prompt)
      setGeneratedSegments(segments)
      setIsGenerating(false)
      
      // Generate a name based on the prompt
      const words = prompt.split(' ')
      const name = words.slice(0, 5).join(' ') + ' Timer'
      setTimerName(name.charAt(0).toUpperCase() + name.slice(1))
    }, 1500)
  }

  const parsePromptToSegments = (text: string): Array<{ name: string; duration: number }> => {
    // Simple parsing logic - in production, this would use AI
    const lowerText = text.toLowerCase()
    
    // Common patterns
    if (lowerText.includes('podcast') || lowerText.includes('interview')) {
      return [
        { name: 'Introduction', duration: 180 },
        { name: 'Guest Background', duration: 300 },
        { name: 'Main Discussion', duration: 1200 },
        { name: 'Q&A Session', duration: 300 },
        { name: 'Closing', duration: 120 }
      ]
    }
    
    if (lowerText.includes('presentation') || lowerText.includes('pitch')) {
      return [
        { name: 'Opening Hook', duration: 60 },
        { name: 'Problem Statement', duration: 180 },
        { name: 'Solution Overview', duration: 300 },
        { name: 'Demo/Examples', duration: 480 },
        { name: 'Call to Action', duration: 120 }
      ]
    }
    
    if (lowerText.includes('meeting') || lowerText.includes('standup')) {
      return [
        { name: 'Check-in', duration: 180 },
        { name: 'Updates', duration: 600 },
        { name: 'Discussion', duration: 600 },
        { name: 'Action Items', duration: 180 }
      ]
    }
    
    // Default segments based on mentioned duration
    const durationMatch = text.match(/(\d+)\s*(min|minute|hour)/i)
    const totalMinutes = durationMatch ? parseInt(durationMatch[1]) : 30
    const totalSeconds = totalMinutes * 60
    
    return [
      { name: 'Introduction', duration: Math.floor(totalSeconds * 0.1) },
      { name: 'Main Content', duration: Math.floor(totalSeconds * 0.7) },
      { name: 'Conclusion', duration: Math.floor(totalSeconds * 0.2) }
    ]
  }

  const handleCreate = () => {
    if (generatedSegments.length > 0) {
      onCreate(generatedSegments, timerName)
      // Reset state
      setPrompt('')
      setGeneratedSegments([])
      setTimerName('AI Generated Timer')
    }
  }

  const reset = () => {
    setPrompt('')
    setGeneratedSegments([])
    setTimerName('AI Generated Timer')
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", duration: 0.3 }}
            className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl z-50"
          >
            <div className="bg-gray-900 rounded-2xl shadow-2xl border border-gray-800/50 overflow-hidden">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-800/50">
                <h2 className="text-xl font-bold text-white flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-purple-500" />
                  AI Timer Assistant
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-800/50 rounded-lg transition"
                >
                  <X className="w-5 h-5 text-gray-400" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6">
                {generatedSegments.length === 0 ? (
                  <>
                    {/* Prompt Input */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        Describe your timer needs
                      </label>
                      <textarea
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        placeholder="E.g., '30 minute podcast with intro, main content, and outro' or 'Team meeting with updates and discussion'"
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:border-purple-500/50 transition resize-none"
                        rows={4}
                      />
                    </div>

                    {/* Examples */}
                    <div className="mb-6">
                      <p className="text-sm text-gray-400 mb-3">Try these examples:</p>
                      <div className="flex flex-wrap gap-2">
                        {[
                          '45 minute webinar with Q&A',
                          '20 minute daily standup',
                          '1 hour workshop with breaks',
                          '15 minute video tutorial'
                        ].map((example) => (
                          <button
                            key={example}
                            onClick={() => setPrompt(example)}
                            className="px-3 py-1.5 bg-purple-500/10 hover:bg-purple-500/20 border border-purple-500/30 rounded-lg text-sm text-purple-400 transition"
                          >
                            {example}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Generate Button */}
                    <button
                      onClick={generateSegments}
                      disabled={!prompt.trim() || isGenerating}
                      className="w-full py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-600 disabled:to-gray-700 text-white font-medium rounded-xl transition disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                      {isGenerating ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          <span>Generating...</span>
                        </>
                      ) : (
                        <>
                          <Wand2 className="w-4 h-4" />
                          <span>Generate Timer</span>
                        </>
                      )}
                    </button>
                  </>
                ) : (
                  <>
                    {/* Generated Result */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-white">Generated Timer</h3>
                        <button
                          onClick={reset}
                          className="text-sm text-purple-400 hover:text-purple-300 transition"
                        >
                          Start Over
                        </button>
                      </div>

                      {/* Timer Name */}
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-400 mb-2">
                          Timer Name
                        </label>
                        <input
                          type="text"
                          value={timerName}
                          onChange={(e) => setTimerName(e.target.value)}
                          className="w-full px-4 py-2 bg-gray-800/50 border border-gray-700/50 rounded-xl text-white focus:outline-none focus:border-purple-500/50 transition"
                        />
                      </div>

                      {/* Segments */}
                      <div className="space-y-2">
                        {generatedSegments.map((segment, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg border border-gray-700/50"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center text-sm font-medium text-purple-400">
                                {index + 1}
                              </div>
                              <span className="text-white">{segment.name}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-400">
                              <Clock className="w-3.5 h-3.5" />
                              <span>
                                {Math.floor(segment.duration / 60)}:{(segment.duration % 60).toString().padStart(2, '0')}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Total Duration */}
                      <div className="mt-4 p-3 bg-purple-500/10 rounded-lg border border-purple-500/30">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-purple-400">Total Duration</span>
                          <span className="text-sm font-medium text-purple-300">
                            {Math.floor(generatedSegments.reduce((acc, seg) => acc + seg.duration, 0) / 60)} minutes
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <button
                        onClick={reset}
                        className="flex-1 px-4 py-3 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 font-medium rounded-xl transition"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleCreate}
                        disabled={isCreating}
                        className="flex-1 px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-600 disabled:to-gray-700 text-white font-medium rounded-xl transition disabled:cursor-not-allowed"
                      >
                        {isCreating ? 'Creating...' : 'Create Timer'}
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}