'use client'

import { useState, useEffect, useCallback } from 'react'
import { Timer } from '@/types/database'
import { createClient } from '@/lib/supabase/client'
import { Play, Pause, RotateCcw, SkipForward, Plus, Minus, Crown, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { UserPlan } from '@/lib/subscription'
import { isTimerLimitReached, getRemainingTime, formatTimeLimit, TIMER_LIMITS } from '@/lib/timer-limits'

interface TimerControlsProps {
  timer: Timer
  roomId: string
  onNext: () => void
  allTimers?: Timer[]
  userPlan?: UserPlan | null
  onUpgradeNeeded?: () => void
}

export default function TimerControls({ timer, roomId, onNext, allTimers = [], userPlan, onUpgradeNeeded }: TimerControlsProps) {
  const supabase = createClient()
  const [displayTime, setDisplayTime] = useState(0)
  const [isUpdating, setIsUpdating] = useState(false)
  const [hasFinished, setHasFinished] = useState(false)

  // Calculate display time based on timer state
  const calculateDisplayTime = useCallback(() => {
    if (timer.type === 'tod') {
      return 0 // Time of day handled differently
    }

    if (timer.status === 'idle') {
      return timer.duration
    }

    if (timer.status === 'finished') {
      return timer.type === 'countdown' ? 0 : timer.elapsed_time
    }

    if (timer.status === 'running' && timer.started_at) {
      const startTime = new Date(timer.started_at).getTime()
      const now = Date.now()
      const sessionElapsed = Math.floor((now - startTime) / 1000)
      const totalElapsed = timer.elapsed_time + sessionElapsed

      if (timer.type === 'countdown') {
        const remaining = timer.duration - totalElapsed
        // Always stop at 0 for countdown timers to prevent negative display
        return Math.max(0, remaining)
      } else {
        return totalElapsed
      }
    }

    return timer.type === 'countdown' ? timer.duration - timer.elapsed_time : timer.elapsed_time
  }, [timer])

  // Timer control functions
  const finishTimer = async () => {
    setIsUpdating(true)

    // Calculate final elapsed time based on timer type
    let finalElapsedTime: number
    if (timer.type === 'countdown') {
      // For countdown timers, elapsed time should be the full duration when finished
      finalElapsedTime = timer.duration
    } else {
      // For countup timers, use the current display time
      finalElapsedTime = calculateDisplayTime()
    }

    console.log('Finishing timer:', timer.name, 'Final elapsed time:', finalElapsedTime)

    // Mark current timer as finished
    await supabase
      .from('timers')
      .update({
        status: 'finished',
        started_at: null,
        elapsed_time: finalElapsedTime
      })
      .eq('id', timer.id)

    // Auto-start next timer if it exists and is set to linked trigger
    const currentIndex = allTimers.findIndex(t => t.id === timer.id)
    console.log('Current timer finished:', timer.name, 'Index:', currentIndex, 'Total timers:', allTimers.length)

    if (currentIndex < allTimers.length - 1) {
      const nextTimer = allTimers[currentIndex + 1]
      console.log('Next timer:', nextTimer.name, 'Trigger:', nextTimer.trigger)

      // Only auto-start if the next timer has 'linked' trigger
      if (nextTimer.trigger === 'linked') {
        console.log('Auto-starting next timer:', nextTimer.name)
        await supabase
          .from('timers')
          .update({
            status: 'running',
            started_at: new Date().toISOString(),
            elapsed_time: 0
          })
          .eq('id', nextTimer.id)

        // Switch to next timer in UI immediately
        onNext()
      } else {
        console.log('Next timer not set to auto-start (trigger:', nextTimer.trigger, ')')
      }
    } else {
      console.log('No more timers to auto-start')
    }
    setIsUpdating(false)
  }

  // Update display time and handle auto-progression
  useEffect(() => {
    const updateDisplay = () => {
      const newTime = calculateDisplayTime()
      setDisplayTime(newTime)
      
      // Auto-progression: if countdown timer reaches 0 and hasn't finished yet
      if (timer.type === 'countdown' &&
          timer.status === 'running' &&
          newTime <= 0 &&
          !hasFinished) {
        console.log('Timer finished automatically:', timer.name, 'Time:', newTime)
        setHasFinished(true)
        finishTimer()
      }
    }

    updateDisplay()
    
    if (timer.status === 'running') {
      const interval = setInterval(updateDisplay, 100)
      return () => clearInterval(interval)
    }
  }, [timer, calculateDisplayTime, hasFinished, finishTimer])

  // Reset hasFinished when timer changes or is reset
  useEffect(() => {
    if (timer.status === 'idle' || timer.status === 'paused') {
      setHasFinished(false)
    }
  }, [timer.status])

  // Reset hasFinished when timer ID changes (switching to different timer)
  useEffect(() => {
    setHasFinished(false)
  }, [timer.id])

  // Format time display
  const formatTime = (seconds: number) => {
    if (timer.type === 'tod') {
      const now = new Date()
      return now.toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      })
    }

    const absSeconds = Math.abs(seconds)
    const hours = Math.floor(absSeconds / 3600)
    const mins = Math.floor((absSeconds % 3600) / 60)
    const secs = absSeconds % 60

    let timeStr = ''
    if (hours > 0) {
      timeStr = `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    } else {
      timeStr = `${mins}:${secs.toString().padStart(2, '0')}`
    }

    return seconds < 0 ? `-${timeStr}` : timeStr
  }

  // Timer control functions
  const startTimer = async () => {
    // Check if free user is trying to start a timer that would exceed the limit
    if (userPlan?.plan_type === 'free' && timer.type === 'countup') {
      const currentElapsed = timer.elapsed_time || 0
      if (isTimerLimitReached('free', currentElapsed)) {
        onUpgradeNeeded?.()
        return
      }
    }

    setIsUpdating(true)
    await supabase
      .from('timers')
      .update({
        status: 'running',
        started_at: new Date().toISOString()
      })
      .eq('id', timer.id)
    setIsUpdating(false)
  }

  const pauseTimer = async () => {
    setIsUpdating(true)

    // Calculate how much time has actually elapsed since start
    const startTime = new Date(timer.started_at!).getTime()
    const now = Date.now()
    const sessionElapsed = Math.floor((now - startTime) / 1000)
    const totalElapsed = timer.elapsed_time + sessionElapsed

    await supabase
      .from('timers')
      .update({
        status: 'paused',
        elapsed_time: Math.max(0, totalElapsed),
        started_at: null
      })
      .eq('id', timer.id)
    setIsUpdating(false)
  }

  const resetTimer = async () => {
    setIsUpdating(true)
    await supabase
      .from('timers')
      .update({
        status: 'idle',
        elapsed_time: 0,
        started_at: null
      })
      .eq('id', timer.id)
    setIsUpdating(false)
  }

  const adjustTime = async (seconds: number) => {
    setIsUpdating(true)
    const currentElapsed = timer.elapsed_time
    const newElapsed = Math.max(0, currentElapsed - seconds)

    await supabase
      .from('timers')
      .update({
        elapsed_time: newElapsed
      })
      .eq('id', timer.id)
    setIsUpdating(false)
  }

  // Determine if timer is in wrap-up time
  const isWrapUp = timer.type === 'countdown' && 
    displayTime > 0 && 
    displayTime <= timer.wrap_up_time

  const isOvertime = timer.type === 'countdown' && displayTime < 0

  return (
    <div className="text-center">
      {/* Timer Name */}
      <h2 className="text-2xl font-semibold mb-8 text-gray-300">{timer.name}</h2>

      {/* Time Display */}
      <div className="mb-12 text-center">
        <div className="relative">
          {/* Glow effect */}
          <div className={cn(
            "absolute inset-0 blur-2xl opacity-30 transition-colors duration-500",
            isOvertime ? "bg-red-500/50" : isWrapUp ? "bg-yellow-500/50" : "bg-white/30"
          )} />

          <div
            className={cn(
              "relative font-mono text-6xl lg:text-8xl font-bold tracking-tight transition-all duration-500 drop-shadow-2xl",
              isOvertime ? "text-red-400 animate-pulse" :
              isWrapUp ? "text-yellow-400" :
              "text-white"
            )}
          >
            {formatTime(displayTime)}
          </div>
        </div>

        {timer.type !== 'tod' && (
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-center space-x-4">
              <div className={cn(
                "px-4 py-2 rounded-full border text-sm font-medium",
                timer.status === 'running' ? "border-green-500/50 bg-green-500/10 text-green-400" :
                timer.status === 'paused' ? "border-yellow-500/50 bg-yellow-500/10 text-yellow-400" :
                "border-gray-700 bg-gray-800/50 text-gray-400"
              )}>
                {timer.type === 'countdown' ? 'Countdown' : 'Count Up'} Timer
              </div>
              <div className={cn(
                "flex items-center space-x-2 px-3 py-1 rounded-full text-xs",
                timer.status === 'running' ? "text-green-400" :
                timer.status === 'paused' ? "text-yellow-400" :
                "text-gray-500"
              )}>
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  timer.status === 'running' ? "bg-green-500 animate-pulse" :
                  timer.status === 'paused' ? "bg-yellow-500" :
                  "bg-gray-500"
                )} />
                <span className="capitalize">{timer.status}</span>
              </div>
            </div>

            {/* Free Plan Timer Limit Warning */}
            {userPlan?.plan_type === 'free' && timer.type === 'countup' && (
              <div className="flex items-center justify-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-amber-400" />
                <span className="text-sm text-amber-400">
                  {timer.status === 'running' ? (
                    <>
                      <span className={cn(
                        getRemainingTime('free', timer.elapsed_time || 0) <= 60 ? 'text-red-400 font-semibold' : ''
                      )}>
                        {formatTimeLimit(getRemainingTime('free', timer.elapsed_time || 0))} remaining
                      </span>
                      {' '}on free plan
                    </>
                  ) : (
                    `Free plan limit: ${formatTimeLimit(TIMER_LIMITS.FREE)}`
                  )}
                </span>
                {onUpgradeNeeded && (
                  <button
                    onClick={onUpgradeNeeded}
                    className="ml-2 text-xs px-2 py-1 bg-amber-500/20 hover:bg-amber-500/30 text-amber-400 rounded border border-amber-500/30 transition-colors"
                  >
                    <Crown className="w-3 h-3 inline mr-1" />
                    Upgrade
                  </button>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {timer.type === 'countdown' && (
        <div className="mb-12 max-w-2xl mx-auto">
          <div className="relative">
            <div className="h-3 bg-gray-800/50 rounded-full overflow-hidden backdrop-blur-sm border border-gray-700/50">
              <div
                className={cn(
                  "h-full transition-all duration-500 relative",
                  isOvertime ? "bg-gradient-to-r from-red-500 to-red-600" :
                  isWrapUp ? "bg-gradient-to-r from-yellow-500 to-orange-500" :
                  "bg-gradient-to-r from-green-500 to-emerald-500"
                )}
                style={{
                  width: `${Math.max(0, Math.min(100, ((timer.duration - Math.abs(displayTime)) / timer.duration) * 100))}%`
                }}
              >
                {/* Glow effect */}
                <div className={cn(
                  "absolute inset-0 blur-sm opacity-50",
                  isOvertime ? "bg-red-500" :
                  isWrapUp ? "bg-yellow-500" :
                  "bg-green-500"
                )} />
              </div>
            </div>

            {/* Progress percentage */}
            <div className="mt-2 text-center text-sm text-gray-400">
              {displayTime <= 0 
                ? '100% complete' 
                : `${Math.round(Math.max(0, Math.min(100, (displayTime / timer.duration) * 100)))}% remaining`
              }
            </div>
          </div>
        </div>
      )}

      {/* Timer Info Only - Controls moved to session level */}
      <div className="text-center">
        <div className="text-xl text-gray-300 mb-2 font-semibold">
          {timer.name}
        </div>
        <div className="text-sm text-gray-500">
          Segment {allTimers.findIndex(t => t.id === timer.id) + 1} of {allTimers.length}
        </div>
      </div>

      {/* Timer Status */}
      <div className="mt-8 text-sm text-gray-500">
        {timer.trigger === 'linked' && (
          <p>This timer will auto-start when the previous timer finishes</p>
        )}
        {timer.trigger === 'scheduled' && timer.scheduled_start && (
          <p>Scheduled to start at {new Date(timer.scheduled_start).toLocaleTimeString()}</p>
        )}
      </div>
    </div>
  )
}