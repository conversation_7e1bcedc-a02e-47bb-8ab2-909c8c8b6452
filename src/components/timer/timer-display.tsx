'use client'

import { useState, useEffect } from 'react'
import { Timer } from '@/types/database'
import { cn } from '@/lib/utils'

interface TimerDisplayProps {
  timer: Timer
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export default function TimerDisplay({ timer, className, size = 'md' }: TimerDisplayProps) {
  const [displayTime, setDisplayTime] = useState(0)

  // Calculate current display time
  const calculateDisplayTime = () => {
    if (timer.type === 'tod') {
      // Time of day - show current time
      return new Date().getTime()
    }

    if (timer.status === 'running' && timer.started_at) {
      const startTime = new Date(timer.started_at).getTime()
      const now = Date.now()
      const sessionElapsed = Math.floor((now - startTime) / 1000)
      const totalElapsed = timer.elapsed_time + sessionElapsed

      if (timer.type === 'countdown') {
        return Math.max(0, timer.duration - totalElapsed)
      } else {
        return totalElapsed
      }
    } else {
      // Paused or idle
      if (timer.type === 'countdown') {
        return Math.max(0, timer.duration - timer.elapsed_time)
      } else {
        return timer.elapsed_time
      }
    }
  }

  // Update display time
  useEffect(() => {
    const updateDisplay = () => {
      setDisplayTime(calculateDisplayTime())
    }

    updateDisplay()
    
    if (timer.status === 'running') {
      const interval = setInterval(updateDisplay, 100)
      return () => clearInterval(interval)
    }
  }, [timer])

  // Format time display
  const formatTime = (seconds: number) => {
    if (timer.type === 'tod') {
      return new Date(seconds).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit',
        hour12: false 
      })
    }

    const isNegative = seconds < 0
    const absSeconds = Math.abs(seconds)
    const hours = Math.floor(absSeconds / 3600)
    const mins = Math.floor((absSeconds % 3600) / 60)
    const secs = Math.floor(absSeconds % 60)

    let timeStr = ''
    if (hours > 0) {
      timeStr = `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    } else {
      timeStr = `${mins}:${secs.toString().padStart(2, '0')}`
    }

    return isNegative ? `-${timeStr}` : timeStr
  }

  // Determine if timer is in wrap-up time
  const isWrapUp = timer.type === 'countdown' && 
    displayTime > 0 && 
    displayTime <= timer.wrap_up_time

  const isOvertime = timer.type === 'countdown' && displayTime < 0

  // Size classes
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-4xl'
  }

  return (
    <div className={cn('text-center', className)}>
      <div className="relative">
        {/* Glow effect */}
        <div className={cn(
          "absolute inset-0 blur-xl opacity-20 transition-colors duration-500",
          isOvertime ? "bg-red-500/50" : isWrapUp ? "bg-yellow-500/50" : "bg-white/30"
        )} />

        <div
          className={cn(
            "relative font-mono font-bold tracking-tight transition-all duration-500 drop-shadow-lg",
            sizeClasses[size],
            isOvertime ? "text-red-400 animate-pulse" :
            isWrapUp ? "text-yellow-400" :
            "text-white"
          )}
        >
          {formatTime(displayTime)}
        </div>
      </div>

      {/* Status indicators */}
      {isWrapUp && (
        <div className="mt-2 text-xs text-yellow-400 font-medium">
          Wrap-up time
        </div>
      )}
      {isOvertime && (
        <div className="mt-2 text-xs text-red-400 font-medium animate-pulse">
          Overtime
        </div>
      )}
    </div>
  )
}
