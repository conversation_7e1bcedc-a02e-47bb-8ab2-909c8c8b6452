'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, <PERSON><PERSON><PERSON><PERSON><PERSON>, Zap } from 'lucide-react'

interface FlashMessageModalProps {
  isOpen: boolean
  onClose: () => void
  onSend: (message: string, duration: number) => void
  isSending?: boolean
}

const presetMessages = [
  { text: "Time to wrap up!", duration: 5 },
  { text: "Please speak for 3 more minutes", duration: 7 },
  { text: "Technical issue - please pause", duration: 5 },
  { text: "Speed up the pace", duration: 4 },
  { text: "Slow down a bit", duration: 4 },
  { text: "Check your microphone", duration: 5 },
  { text: "Look at the audience", duration: 3 },
  { text: "Last question", duration: 4 }
]

export default function FlashMessageModal({ 
  isOpen, 
  onClose, 
  onSend, 
  isSending 
}: FlashMessageModalProps) {
  const [message, setMessage] = useState('')
  const [duration, setDuration] = useState(5)

  const handleSend = () => {
    if (message.trim()) {
      onSend(message.trim(), duration)
      // Reset state
      setMessage('')
      setDuration(5)
    }
  }

  const handlePresetClick = (preset: typeof presetMessages[0]) => {
    setMessage(preset.text)
    setDuration(preset.duration)
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", duration: 0.3 }}
            className="fixed inset-0 flex items-center justify-center p-4 z-50"
          >
            <div className="bg-gray-900 rounded-2xl shadow-2xl border border-gray-800/50 w-full max-w-lg max-h-[90vh] flex flex-col">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-800/50 flex-shrink-0">
                <h2 className="text-xl font-bold text-white flex items-center gap-2">
                  <Zap className="w-5 h-5 text-yellow-500" />
                  Flash Message to Speaker
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-800/50 rounded-lg transition"
                >
                  <X className="w-5 h-5 text-gray-400" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 space-y-4 overflow-y-auto flex-1">
                {/* Message Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Urgent Message
                  </label>
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Type your urgent message..."
                    rows={3}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:border-yellow-500/50 transition resize-none"
                    autoFocus
                  />
                </div>

                {/* Duration Slider */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Display Duration: {duration} seconds
                  </label>
                  <input
                    type="range"
                    min="3"
                    max="10"
                    value={duration}
                    onChange={(e) => setDuration(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-yellow-500"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>3s</span>
                    <span>10s</span>
                  </div>
                </div>

                {/* Preset Messages */}
                <div>
                  <p className="text-sm text-gray-400 mb-3">Quick Messages:</p>
                  <div className="grid grid-cols-2 gap-2">
                    {presetMessages.map((preset, index) => (
                      <button
                        key={index}
                        onClick={() => handlePresetClick(preset)}
                        className="px-3 py-2 bg-gray-800/50 hover:bg-gray-700/50 border border-gray-700/50 rounded-lg text-sm text-left transition"
                      >
                        {preset.text}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Warning */}
                <div className="flex items-start gap-2 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5" />
                  <p className="text-sm text-yellow-400">
                    This will flash the screen and display a large message. Use only for urgent communication.
                  </p>
                </div>
              </div>

              {/* Footer */}
              <div className="flex gap-3 p-6 border-t border-gray-800/50 bg-gray-900 flex-shrink-0">
                <button
                  onClick={onClose}
                  disabled={isSending}
                  className="flex-1 px-4 py-3 bg-gray-800 hover:bg-gray-700 text-gray-300 font-medium rounded-xl transition disabled:opacity-50 disabled:cursor-not-allowed border border-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSend}
                  disabled={!message.trim() || isSending}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 disabled:from-gray-600 disabled:to-gray-700 text-black font-bold rounded-xl transition disabled:cursor-not-allowed flex items-center justify-center gap-2 shadow-lg"
                >
                  {isSending ? (
                    <>
                      <div className="w-4 h-4 border-2 border-black/30 border-t-black rounded-full animate-spin" />
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4" />
                      <span>Send Flash Message</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}