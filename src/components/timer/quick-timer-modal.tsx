'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Clock, Plus, Minus } from 'lucide-react'

interface QuickTimerModalProps {
  isOpen: boolean
  onClose: () => void
  onCreate: (duration: number, name: string) => void
  isCreating?: boolean
}

export default function QuickTimerModal({ isOpen, onClose, onCreate, isCreating }: QuickTimerModalProps) {
  const [minutes, setMinutes] = useState(5)
  const [name, setName] = useState('Quick Timer')

  const handleCreate = () => {
    onCreate(minutes * 60, name)
  }

  const adjustMinutes = (delta: number) => {
    setMinutes(prev => Math.max(1, Math.min(60, prev + delta)))
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", duration: 0.3 }}
            className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md z-50"
          >
            <div className="bg-gray-900 rounded-2xl shadow-2xl border border-gray-800/50 overflow-hidden">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-800/50">
                <h2 className="text-xl font-bold text-white flex items-center gap-2">
                  <Clock className="w-5 h-5 text-green-500" />
                  Quick Timer
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-800/50 rounded-lg transition"
                >
                  <X className="w-5 h-5 text-gray-400" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 space-y-6">
                {/* Timer Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Timer Name
                  </label>
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:outline-none focus:border-green-500/50 transition"
                    placeholder="Enter timer name"
                  />
                </div>

                {/* Duration Selector */}
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-4">
                    Duration
                  </label>
                  <div className="flex items-center justify-center gap-4">
                    <button
                      onClick={() => adjustMinutes(-5)}
                      className="p-3 bg-gray-800/50 hover:bg-gray-700/50 rounded-xl transition"
                    >
                      <Minus className="w-5 h-5" />
                    </button>
                    
                    <div className="text-center">
                      <div className="text-5xl font-bold text-white">
                        {minutes}
                      </div>
                      <div className="text-sm text-gray-400 mt-1">minutes</div>
                    </div>
                    
                    <button
                      onClick={() => adjustMinutes(5)}
                      className="p-3 bg-gray-800/50 hover:bg-gray-700/50 rounded-xl transition"
                    >
                      <Plus className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Quick presets */}
                  <div className="flex gap-2 mt-4 justify-center">
                    {[5, 10, 15, 30].map(preset => (
                      <button
                        key={preset}
                        onClick={() => setMinutes(preset)}
                        className={`px-3 py-1.5 rounded-lg text-sm font-medium transition ${
                          minutes === preset
                            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                            : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'
                        }`}
                      >
                        {preset}m
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="flex gap-3 p-6 border-t border-gray-800/50">
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-3 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 font-medium rounded-xl transition"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreate}
                  disabled={isCreating || !name.trim()}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-gray-600 disabled:to-gray-700 text-black font-medium rounded-xl transition disabled:cursor-not-allowed"
                >
                  {isCreating ? 'Creating...' : 'Create Timer'}
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}