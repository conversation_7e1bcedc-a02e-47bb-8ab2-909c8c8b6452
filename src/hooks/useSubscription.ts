'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { UserPlan, UserSubscription } from '@/lib/subscription'

interface UseSubscriptionReturn {
  plan: UserPlan | null
  subscriptions: UserSubscription[]
  loading: boolean
  error: string | null
  syncSubscription: () => Promise<void>
  createCheckout: (productId: string) => Promise<string | null>
}

export function useSubscription(): UseSubscriptionReturn {
  const [plan, setPlan] = useState<UserPlan | null>(null)
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/subscription/status')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch subscription data')
      }

      setPlan(data.plan)
      setSubscriptions(data.subscriptions || [])
    } catch (err) {
      console.error('Error fetching subscription data:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const syncSubscription = async () => {
    try {
      setError(null)

      const response = await fetch('/api/subscription/sync', {
        method: 'POST',
      })
      
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sync subscription')
      }

      setPlan(data.plan)
      
      // Refetch subscription data to get updated subscriptions
      await fetchSubscriptionData()
    } catch (err) {
      console.error('Error syncing subscription:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      throw err
    }
  }

  const createCheckout = async (productId: string): Promise<string | null> => {
    try {
      setError(null)

      const response = await fetch('/api/subscription/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productId }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session')
      }

      return data.checkoutUrl
    } catch (err) {
      console.error('Error creating checkout:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      throw err
    }
  }

  useEffect(() => {
    fetchSubscriptionData()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          fetchSubscriptionData()
        } else if (event === 'SIGNED_OUT') {
          setPlan(null)
          setSubscriptions([])
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  // Listen for real-time changes to user_plans
  useEffect(() => {
    const channel = supabase
      .channel('user_plans_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_plans',
        },
        () => {
          fetchSubscriptionData()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [supabase])

  return {
    plan,
    subscriptions,
    loading,
    error,
    syncSubscription,
    createCheckout,
  }
}