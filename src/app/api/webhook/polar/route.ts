import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { subscriptionService } from '@/lib/subscription'
import { POLAR_WEBHOOK_SECRET } from '@/lib/polar'
import { createClient } from '@/lib/supabase/server'
import { createClient as createAdminClient } from '@supabase/supabase-js'
import { validateEvent, WebhookVerificationError } from '@polar-sh/sdk/webhooks'

// Admin client for webhook operations (bypasses RLS)
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
)

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = headers()
    
    // Use Polar.sh SDK for webhook verification
    let event
    try {
      event = validateEvent(
        body,
        Object.fromEntries(headersList.entries()),
        POLAR_WEBHOOK_SECRET
      )
      console.log('✅ Webhook signature verified successfully')
    } catch (error) {
      if (error instanceof WebhookVerificationError) {
        console.error('❌ Webhook signature verification failed:', error.message)
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
      }
      throw error
    }

    console.log('=== POLAR WEBHOOK EVENT ===')
    console.log('Event type:', event.type)
    console.log('Event data:', JSON.stringify(event.data, null, 2))
    console.log('==========================')

    // Handle different event types
    switch (event.type) {
      case 'subscription.created':
      case 'subscription.active':
      case 'subscription.updated':
        await handleSubscriptionEvent(event.data)
        break

      case 'subscription.canceled':
      case 'subscription.uncanceled':
      case 'subscription.revoked':
        await handleSubscriptionEvent(event.data)
        break

      case 'order.paid':
      case 'order.updated':
        // Handle order events that contain subscription data
        if (event.data.subscription) {
          console.log('Processing order with subscription data...')
          await handleSubscriptionEvent(event.data.subscription)
        }
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
        // Let's handle any subscription-related event for testing
        if (event.type.startsWith('subscription.')) {
          console.log('Processing unknown subscription event...')
          await handleSubscriptionEvent(event.data)
        }
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}


async function handleSubscriptionEvent(subscriptionData: any) {
  try {
    console.log('=== PROCESSING SUBSCRIPTION EVENT ===')
    console.log('Subscription ID:', subscriptionData.id)
    console.log('Customer ID:', subscriptionData.customer_id)
    console.log('Status:', subscriptionData.status)
    console.log('Product ID:', subscriptionData.product_id)
    
    // Use admin client for webhook operations
    const supabase = supabaseAdmin

    // Find user by customer ID
    const { data: existingSubscription } = await supabase
      .from('user_subscriptions')
      .select('user_id')
      .eq('polar_customer_id', subscriptionData.customer_id)
      .limit(1)
      .single()

    console.log('Looking for customer ID:', subscriptionData.customer_id)

    console.log('Existing subscription found:', !!existingSubscription)

    let userId: string

    if (existingSubscription) {
      userId = existingSubscription.user_id
      console.log('Found existing subscription for user:', userId)
    } else {
      // For new subscriptions, try to get user ID from metadata first
      if (subscriptionData.metadata?.user_id) {
        userId = subscriptionData.metadata.user_id
        console.log('Using user ID from metadata:', userId)
      } else {
        // Fallback: find user by customer email
        console.log('Customer data in webhook:', subscriptionData.customer)
        
        if (!subscriptionData.customer?.email) {
          console.error('No customer email in subscription data:', subscriptionData.id)
          return
        }

        const { data: users } = await supabase.auth.admin.listUsers()
        const user = users.users.find(u => u.email === subscriptionData.customer?.email)

        if (!user) {
          console.error('Could not find user for email:', subscriptionData.customer.email)
          return
        }

        userId = user.id
        console.log('Found user by email:', userId)
      }
    }

    // Upsert subscription data
    const subscriptionRecord = {
      user_id: userId,
      polar_customer_id: subscriptionData.customer_id,
      polar_subscription_id: subscriptionData.id,
      polar_product_id: subscriptionData.product_id,
      polar_price_id: subscriptionData.price_id,
      status: subscriptionData.status,
      current_period_start: subscriptionData.current_period_start,
      current_period_end: subscriptionData.current_period_end,
      cancel_at_period_end: subscriptionData.cancel_at_period_end || false,
      started_at: subscriptionData.started_at,
      ended_at: subscriptionData.ended_at || null,
    }

    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .upsert(subscriptionRecord, {
        onConflict: 'polar_subscription_id'
      })

    if (subscriptionError) {
      console.error('Error updating subscription:', subscriptionError)
      return
    }

    // Update user plan based on subscription status
    await updateUserPlanFromSubscription(userId, subscriptionData)

    console.log(`Successfully processed subscription event for user ${userId}`)
  } catch (error) {
    console.error('Error handling subscription event:', error)
    throw error
  }
}

async function updateUserPlanFromSubscription(userId: string, subscriptionData: any) {
  console.log('=== UPDATING USER PLAN ===')
  console.log('User ID:', userId)
  console.log('Subscription status:', subscriptionData.status)
  
  // Use admin client for webhook operations
  const supabase = supabaseAdmin

  // Determine plan type based on subscription status and product
  let planType: 'free' | 'pro' | 'enterprise' = 'free'
  let subscriptionId: string | null = null

  if (subscriptionData.status === 'active') {
    // Get product info to determine plan type
    const { data: product } = await supabase
      .from('products')
      .select('plan_type, id')
      .eq('polar_product_id', subscriptionData.product_id)
      .single()

    if (product) {
      planType = product.plan_type
      
      // Get the subscription record ID
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('id')
        .eq('polar_subscription_id', subscriptionData.id)
        .single()

      if (subscription) {
        subscriptionId = subscription.id
      }
    }
  }

  console.log('Final plan type:', planType)
  console.log('Final subscription ID:', subscriptionId)

  // Update user plan
  const { error } = await supabase
    .from('user_plans')
    .upsert({
      user_id: userId,
      plan_type: planType,
      is_active: true,
      subscription_id: subscriptionId,
    }, {
      onConflict: 'user_id'
    })

  if (error) {
    console.error('Error updating user plan:', error)
    throw error
  }
  
  console.log('User plan updated successfully to:', planType)
}