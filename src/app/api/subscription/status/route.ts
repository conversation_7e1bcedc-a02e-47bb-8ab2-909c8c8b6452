import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('Auth error:', authError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('User authenticated:', user.email)

    // Check if user_plans table exists and get user plan
    let userPlan = null
    try {
      // First try to check if the table exists by attempting a simple query
      const { data: planData, error: planError } = await supabase
        .from('user_plans')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (planError) {
        console.log('Plan fetch error:', planError)
        
        // If table doesn't exist or no plan exists, try to create one
        if (planError.code === 'PGRST116' || planError.message?.includes('relation') || planError.message?.includes('does not exist')) {
          console.log('user_plans table may not exist, returning default plan')
          userPlan = {
            id: 'default',
            user_id: user.id,
            plan_type: 'free',
            is_active: true,
            subscription_id: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
        } else if (planError.code === 'PGRST116') {
          // No plan found, try to create one
          try {
            const { data: newPlan, error: createError } = await supabase
              .from('user_plans')
              .insert({
                user_id: user.id,
                plan_type: 'free',
                is_active: true,
              })
              .select()
              .single()

            if (createError) {
              console.log('Error creating plan:', createError)
              userPlan = {
                id: 'default',
                user_id: user.id,
                plan_type: 'free',
                is_active: true,
                subscription_id: null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              }
            } else {
              userPlan = newPlan
            }
          } catch (createErr) {
            console.log('Error in plan creation:', createErr)
            userPlan = {
              id: 'default',
              user_id: user.id,
              plan_type: 'free',
              is_active: true,
              subscription_id: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }
          }
        } else {
          // Other error, return default
          userPlan = {
            id: 'default',
            user_id: user.id,
            plan_type: 'free',
            is_active: true,
            subscription_id: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
        }
      } else {
        userPlan = planData
      }
    } catch (error) {
      console.log('Database error:', error)
      // Return default free plan if database error
      userPlan = {
        id: 'default',
        user_id: user.id,
        plan_type: 'free',
        is_active: true,
        subscription_id: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    }

    return NextResponse.json({ 
      plan: userPlan,
      subscriptions: [],
      user: {
        id: user.id,
        email: user.email,
      }
    })
  } catch (error) {
    console.error('Error fetching subscription status:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}