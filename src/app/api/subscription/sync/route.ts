import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { polar } from '@/lib/polar'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('Sync subscription requested for user:', user.email)

    // Get all subscriptions from Polar
    const subscriptions = await polar.subscriptions.list()
    console.log('Found subscriptions from Polar:', subscriptions.data?.items?.length || 0)

    let userSubscription = null
    let foundCustomer = null

    // Find subscription for this user by email
    for (const subscription of subscriptions.data?.items || []) {
      console.log('Checking subscription:', subscription.id, 'customer:', subscription.customer?.email)
      
      if (subscription.customer?.email === user.email) {
        userSubscription = subscription
        foundCustomer = subscription.customer
        console.log('Found matching subscription:', subscription.id, 'status:', subscription.status)
        break
      }
    }

    if (!userSubscription) {
      console.log('No active subscription found for user:', user.email)
      
      // Return current plan from database or default free
      const { data: existingPlan } = await supabase
        .from('user_plans')
        .select('*')
        .eq('user_id', user.id)
        .single()

      const userPlan = existingPlan || {
        id: 'default',
        user_id: user.id,
        plan_type: 'free',
        is_active: true,
        subscription_id: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      return NextResponse.json({ 
        success: true, 
        plan: userPlan 
      })
    }

    // Update subscription in database
    const subscriptionRecord = {
      user_id: user.id,
      polar_customer_id: foundCustomer.id,
      polar_subscription_id: userSubscription.id,
      polar_product_id: userSubscription.product_id,
      polar_price_id: userSubscription.price_id,
      status: userSubscription.status,
      current_period_start: userSubscription.current_period_start,
      current_period_end: userSubscription.current_period_end,
      cancel_at_period_end: userSubscription.cancel_at_period_end || false,
      started_at: userSubscription.started_at,
      ended_at: userSubscription.ended_at || null,
    }

    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .upsert(subscriptionRecord, {
        onConflict: 'polar_subscription_id'
      })

    if (subscriptionError) {
      console.error('Error updating subscription:', subscriptionError)
    }

    // Update user plan based on subscription status
    let planType: 'free' | 'pro' | 'enterprise' = 'free'
    let subscriptionId: string | null = null

    if (userSubscription.status === 'active') {
      // For now, assume all active subscriptions are pro
      planType = 'pro'
      
      // Get the subscription record ID
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('id')
        .eq('polar_subscription_id', userSubscription.id)
        .single()

      if (subscription) {
        subscriptionId = subscription.id
      }
    }

    // Update user plan
    const { data: planData, error: planError } = await supabase
      .from('user_plans')
      .upsert({
        user_id: user.id,
        plan_type: planType,
        is_active: true,
        subscription_id: subscriptionId,
      }, {
        onConflict: 'user_id'
      })
      .select()

    if (planError) {
      console.error('Error updating user plan:', planError)
      return NextResponse.json({ error: 'Failed to update user plan', details: planError }, { status: 500 })
    }

    console.log('Successfully synced subscription for user:', user.email, 'Plan:', planType)

    return NextResponse.json({ 
      success: true, 
      plan: planData?.[0] || {
        id: 'default',
        user_id: user.id,
        plan_type: planType,
        is_active: true,
        subscription_id: subscriptionId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
    })
  } catch (error) {
    console.error('Error syncing subscription:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user plan and subscriptions
    const [userPlan, subscriptions] = await Promise.all([
      subscriptionService.getUserPlan(user.id),
      subscriptionService.getUserSubscriptions(user.id),
    ])

    return NextResponse.json({ 
      plan: userPlan,
      subscriptions: subscriptions
    })
  } catch (error) {
    console.error('Error fetching subscription data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}