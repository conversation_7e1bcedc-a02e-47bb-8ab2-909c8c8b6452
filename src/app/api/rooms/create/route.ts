import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { nanoid } from 'nanoid'

export async function POST(req: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError) {
      console.error('Auth error:', authError)
      return NextResponse.json({ error: 'Authentication failed', details: authError.message }, { status: 401 })
    }
    if (!user) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 })
    }

    const body = await req.json()
    const { name, description, type = 'manual', timerConfig } = body

    // Generate unique slug
    const slug = nanoid(8)

    // Create room
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .insert({
        name: name || `Timer Room ${new Date().toLocaleDateString()}`,
        // description: description || null, // TODO: Add this field to Supabase database
        slug,
        owner_id: user.id,
        settings: {},
        theme: {
          primaryColor: '#00DC82',
          secondaryColor: '#FF0080'
        }
      })
      .select()
      .single()

    if (roomError) {
      console.error('Error creating room:', roomError)
      return NextResponse.json({ error: 'Failed to create room', details: roomError.message }, { status: 500 })
    }

    // Create initial timer based on type
    if (type === 'quick' && timerConfig) {
      const { data: timer, error: timerError } = await supabase
        .from('timers')
        .insert({
          room_id: room.id,
          name: timerConfig.name || 'Quick Timer',
          duration: timerConfig.duration || 300, // 5 minutes default
          type: 'countdown',
          position: 0
        })
        .select()

      if (timerError) {
        console.error('Error creating timer:', timerError)
      }
    } else if (type === 'ai' && timerConfig?.segments) {
      // Create multiple timers for AI-generated segments
      const timers = timerConfig.segments.map((segment: any, index: number) => ({
        room_id: room.id,
        name: segment.name,
        duration: segment.duration,
        type: 'countdown',
        position: index,
        trigger: index > 0 ? 'linked' : 'manual',
        linked_to_timer_id: null // Will update after creation
      }))

      const { data: createdTimers, error: timersError } = await supabase
        .from('timers')
        .insert(timers)
        .select()

      if (timersError) {
        console.error('Error creating timers:', timersError)
      }

      // Update linked timer IDs
      if (createdTimers && createdTimers.length > 1) {
        for (let i = 1; i < createdTimers.length; i++) {
          await supabase
            .from('timers')
            .update({ linked_to_timer_id: createdTimers[i - 1].id })
            .eq('id', createdTimers[i].id)
        }
      }
    }

    return NextResponse.json({
      room,
      viewerUrl: `/r/${slug}`,
      controllerUrl: `/r/${slug}/controller`
    })
  } catch (error) {
    console.error('Error in room creation:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}