import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Get all rooms
    const { data: rooms, error } = await supabase
      .from('rooms')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)
    
    return NextResponse.json({
      rooms: rooms || [],
      error: error?.message || null,
      count: rooms?.length || 0
    })
  } catch (error) {
    return NextResponse.json({ 
      error: 'Internal error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}