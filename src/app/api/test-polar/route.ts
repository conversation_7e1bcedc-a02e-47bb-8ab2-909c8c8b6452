import { NextRequest, NextResponse } from 'next/server'
import { polar, POLAR_ORGANIZATION_ID } from '@/lib/polar'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing Polar API connection...')
    console.log('Organization ID:', POLAR_ORGANIZATION_ID)
    
    // Test 1: List products
    console.log('Fetching products...')
    const products = await polar.products.list({
      organizationId: POLAR_ORGANIZATION_ID,
    })
    
    console.log('Products response:', products)

    // Test 2: List customers (should be empty or have test customers)
    console.log('Fetching customers...')
    const customers = await polar.customers.list({
      organizationId: POLAR_ORGANIZATION_ID,
    })
    
    console.log('Customers response:', customers)

    return NextResponse.json({
      success: true,
      data: {
        products: products.data || products,
        customers: customers.data || customers,
        organizationId: POLAR_ORGANIZATION_ID,
      }
    })
  } catch (error) {
    console.error('Polar API test error:', error)
    return NextResponse.json(
      { 
        error: 'Polar API test failed', 
        details: error instanceof Error ? error.message : 'Unknown error',
        organizationId: POLAR_ORGANIZATION_ID,
      },
      { status: 500 }
    )
  }
}