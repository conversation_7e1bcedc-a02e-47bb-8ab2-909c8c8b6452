import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { subscriptionService } from '@/lib/subscription'
import { POLAR_WEBHOOK_SECRET } from '@/lib/polar'
import { createClient } from '@/lib/supabase/server'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = headers()
    const signature = headersList.get('polar-signature')

    // Verify webhook signature
    if (!verifyWebhookSignature(body, signature)) {
      console.error('Invalid webhook signature')
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    }

    const event = JSON.parse(body)
    console.log('Received Polar webhook:', event.type)

    // Handle different event types
    switch (event.type) {
      case 'subscription.created':
      case 'subscription.active':
      case 'subscription.updated':
        await handleSubscriptionEvent(event.data)
        break

      case 'subscription.canceled':
      case 'subscription.uncanceled':
      case 'subscription.revoked':
        await handleSubscriptionEvent(event.data)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function verifyWebhookSignature(body: string, signature: string | null): boolean {
  if (!signature || !POLAR_WEBHOOK_SECRET) {
    return false
  }

  try {
    // Polar.sh uses HMAC-SHA256 for webhook signatures
    const expectedSignature = crypto
      .createHmac('sha256', POLAR_WEBHOOK_SECRET)
      .update(body, 'utf8')
      .digest('hex')

    // Remove 'sha256=' prefix if present
    const cleanSignature = signature.startsWith('sha256=') 
      ? signature.substring(7) 
      : signature

    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(cleanSignature, 'hex')
    )
  } catch (error) {
    console.error('Error verifying signature:', error)
    return false
  }
}

async function handleSubscriptionEvent(subscriptionData: any) {
  try {
    const supabase = await createClient()

    // Find user by customer ID
    const { data: existingSubscription } = await supabase
      .from('user_subscriptions')
      .select('user_id')
      .eq('polar_customer_id', subscriptionData.customer_id)
      .limit(1)
      .single()

    let userId: string

    if (existingSubscription) {
      userId = existingSubscription.user_id
    } else {
      // Try to find user by customer email from Polar
      // Note: Need to get customer info from Polar API since it's not in webhook data
      console.log('Customer data in webhook:', subscriptionData.customer)
      
      if (!subscriptionData.customer?.email) {
        console.error('No customer email in subscription data:', subscriptionData.id)
        return
      }

      const { data: users } = await supabase.auth.admin.listUsers()
      const user = users.users.find(u => u.email === subscriptionData.customer.email)

      if (!user) {
        console.error('Could not find user for email:', subscriptionData.customer.email)
        return
      }

      userId = user.id
    }

    // Upsert subscription data
    const subscriptionRecord = {
      user_id: userId,
      polar_customer_id: subscriptionData.customer_id,
      polar_subscription_id: subscriptionData.id,
      polar_product_id: subscriptionData.product_id,
      polar_price_id: subscriptionData.price_id,
      status: subscriptionData.status,
      current_period_start: subscriptionData.current_period_start,
      current_period_end: subscriptionData.current_period_end,
      cancel_at_period_end: subscriptionData.cancel_at_period_end || false,
      started_at: subscriptionData.started_at,
      ended_at: subscriptionData.ended_at || null,
    }

    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .upsert(subscriptionRecord, {
        onConflict: 'polar_subscription_id'
      })

    if (subscriptionError) {
      console.error('Error updating subscription:', subscriptionError)
      return
    }

    // Update user plan based on subscription status
    await updateUserPlanFromSubscription(userId, subscriptionData)

    console.log(`Successfully processed subscription event for user ${userId}`)
  } catch (error) {
    console.error('Error handling subscription event:', error)
    throw error
  }
}

async function updateUserPlanFromSubscription(userId: string, subscriptionData: any) {
  const supabase = await createClient()

  // Determine plan type based on subscription status and product
  let planType: 'free' | 'pro' | 'enterprise' = 'free'
  let subscriptionId: string | null = null

  if (subscriptionData.status === 'active') {
    // Get product info to determine plan type
    const { data: product } = await supabase
      .from('products')
      .select('plan_type, id')
      .eq('polar_product_id', subscriptionData.product_id)
      .single()

    if (product) {
      planType = product.plan_type
      
      // Get the subscription record ID
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('id')
        .eq('polar_subscription_id', subscriptionData.id)
        .single()

      if (subscription) {
        subscriptionId = subscription.id
      }
    }
  }

  // Update user plan
  const { error } = await supabase
    .from('user_plans')
    .upsert({
      user_id: userId,
      plan_type: planType,
      is_active: true,
      subscription_id: subscriptionId,
    }, {
      onConflict: 'user_id'
    })

  if (error) {
    console.error('Error updating user plan:', error)
    throw error
  }
}