import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  console.log('Test webhook called')
  
  const body = await request.text()
  const headers = Object.fromEntries(request.headers.entries())
  
  console.log('Headers:', headers)
  console.log('Body:', body)
  
  return NextResponse.json({ 
    message: 'Test webhook received',
    timestamp: new Date().toISOString(),
    headers: headers,
    body: body
  })
}

export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    message: 'Webhook test endpoint is working',
    timestamp: new Date().toISOString()
  })
}