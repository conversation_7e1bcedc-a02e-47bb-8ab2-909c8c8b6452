"use client"

import { useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  ArrowRight, 
  Clock, 
  Calendar,
  User,
  Search,
  Tag
} from 'lucide-react'

const blogPosts = [
  {
    slug: 'why-timedflow-perfect-event-timer-2025',
    title: 'Why TimedFlow is the Perfect Event Timer Solution in 2025',
    excerpt: 'Discover why TimedFlow is the modern choice for professional event timing, with superior features, affordable pricing, and flexibility for all your events.',
    date: '2025-01-02',
    author: 'TimedFlow Team',
    category: 'Comparisons',
    readTime: '5 min read',
    image: '/blog/stagetimer-alternative.jpg'
  },
  {
    slug: 'professional-presentation-timing-tips',
    title: '10 Professional Presentation Timing Tips Every Speaker Should Know',
    excerpt: 'Master the art of presentation timing with these proven techniques used by TED speakers and professional presenters worldwide.',
    date: '2025-01-01',
    author: '<PERSON>',
    category: 'Presentation Skills',
    readTime: '7 min read',
    image: '/blog/presentation-timing.jpg'
  },
  {
    slug: 'meeting-productivity-countdown-timer',
    title: 'How to Boost Meeting Productivity with Countdown Timers',
    excerpt: 'Transform your team meetings from time-wasters to productivity powerhouses using strategic countdown timer techniques.',
    date: '2024-12-31',
    author: '<PERSON>',
    category: 'Productivity',
    readTime: '6 min read',
    image: '/blog/meeting-productivity.jpg'
  },
  {
    slug: 'livestream-timing-guide',
    title: 'The Ultimate Guide to Livestream Timing and Audience Engagement',
    excerpt: 'Learn how proper timing can dramatically increase your livestream engagement and keep viewers watching longer.',
    date: '2024-12-30',
    author: 'Lisa Thompson',
    category: 'Content Creation',
    readTime: '8 min read',
    image: '/blog/livestream-guide.jpg'
  },
  {
    slug: 'event-timing-management-best-practices',
    title: 'Event Timing Management: Best Practices for Event Organizers',
    excerpt: 'Professional event organizers share their secrets for keeping events on schedule and attendees engaged.',
    date: '2024-12-29',
    author: 'David Park',
    category: 'Event Management',
    readTime: '9 min read',
    image: '/blog/event-management.jpg'
  },
  {
    slug: 'remote-timer-setup-guide',
    title: 'Remote Timer Setup: Complete Guide for Virtual Events',
    excerpt: 'Step-by-step guide to setting up professional remote timers for virtual events, webinars, and online presentations.',
    date: '2024-12-28',
    author: 'Emma Wilson',
    category: 'Technical Guides',
    readTime: '10 min read',
    image: '/blog/remote-timer-setup.jpg'
  }
]

const categories = ['All', 'Comparisons', 'Presentation Skills', 'Productivity', 'Content Creation', 'Event Management', 'Technical Guides']

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [searchTerm, setSearchTerm] = useState('')

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <nav className="border-b border-white/10 bg-black/95 backdrop-blur-xl sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-6 h-6 text-black" />
                </div>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </Link>
            
            <div className="flex items-center space-x-6">
              <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <Link href="/features" className="text-gray-300 hover:text-white transition-colors">
                Features
              </Link>
              <Link href="/pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
              <Link href="/signup" className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-lg">
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h1 className="text-5xl font-bold mb-6">
              TimedFlow <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">Blog</span>
            </h1>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Master the art of timing with expert tips, guides, and insights for better presentations, meetings, and events.
            </p>
          </motion.div>

          {/* Search and Filter */}
          <div className="mb-12">
            <div className="flex flex-col md:flex-row gap-6 items-center">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-green-500"
                />
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-green-500 text-black'
                        : 'bg-white/5 text-gray-300 hover:bg-white/10'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Blog Posts Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post, index) => (
              <motion.article
                key={post.slug}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="group"
              >
                <Link href={`/blog/${post.slug}`}>
                  <div className="bg-white/5 backdrop-blur-xl rounded-2xl border border-white/10 overflow-hidden hover:border-green-500/30 transition-all duration-300">
                    {/* Image Placeholder */}
                    <div className="h-48 bg-gradient-to-r from-green-500/20 to-emerald-500/20 flex items-center justify-center">
                      <div className="text-4xl font-bold text-white/20">
                        {post.title.split(' ')[0]}
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="px-2 py-1 bg-green-500/10 text-green-400 text-xs rounded-full flex items-center">
                          <Tag className="w-3 h-3 mr-1" />
                          {post.category}
                        </span>
                        <span className="text-gray-500 text-sm">{post.readTime}</span>
                      </div>

                      <h2 className="text-xl font-bold text-white mb-3 group-hover:text-green-400 transition-colors">
                        {post.title}
                      </h2>

                      <p className="text-gray-400 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>

                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center text-gray-500">
                          <User className="w-4 h-4 mr-1" />
                          {post.author}
                        </div>
                        <div className="flex items-center text-gray-500">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(post.date).toLocaleDateString()}
                        </div>
                      </div>

                      <div className="mt-4 flex items-center text-green-400 group-hover:text-green-300 transition-colors">
                        <span className="text-sm font-medium">Read more</span>
                        <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 border-t border-white/10 bg-black">
        <div className="container mx-auto max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <Link href="/" className="flex items-center space-x-3 mb-6 md:mb-0">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-5 h-5 text-black" />
                </div>
              </div>
              <span className="font-bold text-lg bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </Link>
            
            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-400">
              {['Privacy Policy', 'Terms of Service', 'Support', 'API Docs'].map((item) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase().replace(/\s+/g, '-')}`}
                  className="hover:text-white transition-colors"
                >
                  {item}
                </a>
              ))}
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-white/10 text-center text-gray-500 text-sm">
            <p>© 2024 TimedFlow. Built for creators, by creators.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}