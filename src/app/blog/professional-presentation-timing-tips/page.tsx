"use client"

import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  CheckCircle, 
  Lightbulb,
  Target,
  Timer,
  Users,
  Mic
} from 'lucide-react'

export default function PresentationTimingTipsPage() {
  const tips = [
    {
      number: "01",
      title: "The 10-7-3 Rule",
      description: "Plan for 10 minutes of content for every 7 minutes of speaking time, leaving 3 minutes for Q&A and transitions.",
      icon: Timer
    },
    {
      number: "02", 
      title: "Practice with a Timer",
      description: "Always rehearse with an actual countdown timer to build muscle memory for your pacing.",
      icon: Clock
    },
    {
      number: "03",
      title: "Visual Timer for Audience",
      description: "Display a countdown timer visible to your audience to create urgency and engagement.",
      icon: Target
    },
    {
      number: "04",
      title: "Buffer Time Strategy",
      description: "Build 15-20% buffer time into each segment to handle unexpected interruptions or questions.",
      icon: CheckCircle
    },
    {
      number: "05",
      title: "Segment Your Content",
      description: "Break presentations into 5-7 minute segments with clear transitions and micro-breaks.",
      icon: Users
    },
    {
      number: "06",
      title: "The 2-Minute Warning",
      description: "Always give yourself and your audience a 2-minute warning before wrapping up each section.",
      icon: Lightbulb
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Navigation */}
      <nav className="border-b border-white/10 bg-black/95 backdrop-blur-xl sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-6 h-6 text-black" />
                </div>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </Link>
            
            <div className="flex items-center space-x-6">
              <Link href="/blog" className="flex items-center text-gray-300 hover:text-white transition-colors">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Blog
              </Link>
              <Link href="/signup" className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-lg">
                Try TimedFlow Free
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Article */}
      <article className="py-12 px-6">
        <div className="container mx-auto max-w-4xl">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-12"
          >
            <div className="flex items-center gap-3 mb-6">
              <span className="px-3 py-1 bg-blue-500/10 text-blue-400 text-sm rounded-full">
                Presentation Skills
              </span>
              <span className="text-gray-500">7 min read</span>
              <span className="text-gray-500">•</span>
              <span className="text-gray-500">January 1, 2025</span>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
              10 Professional <span className="bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">Presentation Timing</span> Tips Every Speaker Should Know
            </h1>

            <p className="text-xl text-gray-400 leading-relaxed">
              Master the art of presentation timing with these proven techniques used by TED speakers 
              and professional presenters worldwide. Transform your speaking skills with strategic timing.
            </p>
          </motion.header>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="prose prose-invert prose-lg max-w-none"
          >
            <div className="bg-white/5 border border-white/10 rounded-2xl p-8 mb-8">
              <h2 className="text-2xl font-bold mb-4 text-white">Why Timing Matters in Presentations</h2>
              <p className="text-gray-300 mb-4">
                Professional speakers know that timing isn&apos;t just about staying within your allocated slot—it&apos;s about 
                creating rhythm, maintaining engagement, and delivering maximum impact. Poor timing can turn even the 
                best content into a forgettable experience.
              </p>
              <p className="text-gray-300">
                The best presentations feel effortless, but behind that seamless delivery is meticulous timing 
                planning and practice. Let&apos;s explore the techniques that separate amateur speakers from professionals.
              </p>
            </div>

            <h2 className="text-3xl font-bold mb-8 text-white">The Essential Timing Techniques</h2>

            {/* Tips Grid */}
            <div className="grid gap-8 mb-12">
              {tips.slice(0, 6).map((tip, index) => (
                <motion.div
                  key={tip.number}
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 + (index * 0.1) }}
                  className="bg-white/5 border border-white/10 rounded-2xl p-6"
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <tip.icon className="w-8 h-8 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-3">
                        <span className="text-3xl font-bold text-gray-500">{tip.number}</span>
                        <h3 className="text-2xl font-bold text-white">{tip.title}</h3>
                      </div>
                      <p className="text-gray-300 text-lg leading-relaxed">{tip.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <h2 className="text-3xl font-bold mb-6 text-white">Advanced Timing Strategies</h2>

            <h3 className="text-2xl font-semibold mb-4 text-white">The Power of Pause</h3>
            <p className="text-gray-300 mb-6">
              Strategic pauses are one of the most underutilized tools in presentation timing. A well-placed 
              3-second pause can:
            </p>
            <ul className="text-gray-300 mb-6 space-y-2 ml-6">
              <li>• Allow key points to sink in with your audience</li>
              <li>• Give you time to check your timer and adjust pacing</li>
              <li>• Create dramatic effect before important revelations</li>
              <li>• Help you regain composure if you're running behind</li>
            </ul>

            <h3 className="text-2xl font-semibold mb-4 text-white">The Accordion Technique</h3>
            <p className="text-gray-300 mb-6">
              Professional speakers prepare content that can expand or contract based on available time. 
              This "accordion" approach involves:
            </p>
            <ol className="text-gray-300 mb-6 space-y-2 ml-6">
              <li>1. <strong>Core content</strong> (must cover): 60% of your time</li>
              <li>2. <strong>Enhanced examples</strong> (nice to cover): 25% of your time</li>
              <li>3. <strong>Bonus insights</strong> (only if time permits): 15% of your time</li>
            </ol>

            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-8 mb-8">
              <h3 className="text-2xl font-bold mb-4 text-white flex items-center">
                <Mic className="w-6 h-6 mr-3" />
                Tools for Perfect Timing
              </h3>
              <p className="text-gray-300 mb-4">
                The best speakers use professional timing tools to maintain perfect pacing. Here's what you need:
              </p>
              <ul className="text-gray-300 space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span><strong>Remote-controlled timer</strong> visible to both you and your audience</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span><strong>Segment-based countdown</strong> for multi-part presentations</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span><strong>Silent alerts</strong> at key timing milestones</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span><strong>Multi-device sync</strong> for team presentations</span>
                </li>
              </ul>
              <div className="mt-6">
                <Link 
                  href="/signup"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-lg hover:scale-105 transition-transform"
                >
                  Try TimedFlow for Free
                </Link>
              </div>
            </div>

            <h2 className="text-3xl font-bold mb-6 text-white">Common Timing Mistakes to Avoid</h2>

            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6">
                <h4 className="text-xl font-semibold mb-3 text-red-400">❌ The Rush Ending</h4>
                <p className="text-gray-300">
                  Speeding through your conclusion because you&apos;re running out of time. This undermines 
                  your entire presentation.
                </p>
              </div>
              <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6">
                <h4 className="text-xl font-semibold mb-3 text-red-400">❌ Over-Explaining</h4>
                <p className="text-gray-300">
                  Spending too much time on early points and having no time for your key messages.
                </p>
              </div>
              <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6">
                <h4 className="text-xl font-semibold mb-3 text-red-400">❌ Ignoring the Clock</h4>
                <p className="text-gray-300">
                  Not checking time until it's too late to make meaningful adjustments.
                </p>
              </div>
              <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6">
                <h4 className="text-xl font-semibold mb-3 text-red-400">❌ Unrealistic Planning</h4>
                <p className="text-gray-300">
                  Cramming too much content into your allocated time slot.
                </p>
              </div>
            </div>

            <h2 className="text-3xl font-bold mb-6 text-white">Timing for Different Presentation Types</h2>

            <h3 className="text-2xl font-semibold mb-4 text-white">TED-Style Talks (18 minutes)</h3>
            <ul className="text-gray-300 mb-6 space-y-2 ml-6">
              <li>• Minutes 1-2: Hook and problem statement</li>
              <li>• Minutes 3-5: Context and background</li>
              <li>• Minutes 6-14: Main content (3 key points)</li>
              <li>• Minutes 15-17: Call to action and wrap-up</li>
              <li>• Minute 18: Final memorable moment</li>
            </ul>

            <h3 className="text-2xl font-semibold mb-4 text-white">Business Presentations (30-45 minutes)</h3>
            <ul className="text-gray-300 mb-6 space-y-2 ml-6">
              <li>• 10% Opening and agenda</li>
              <li>• 70% Core content with examples</li>
              <li>• 15% Q&A and discussion</li>
              <li>• 5% Summary and next steps</li>
            </ul>

            <h3 className="text-2xl font-semibold mb-4 text-white">Workshop Sessions (2-4 hours)</h3>
            <ul className="text-gray-300 mb-8 space-y-2 ml-6">
              <li>• Break every 45-60 minutes</li>
              <li>• 20-minute activity blocks</li>
              <li>• 10-minute discussion periods</li>
              <li>• 30-minute lunch break for half-day sessions</li>
            </ul>

            <h2 className="text-3xl font-bold mb-6 text-white">Practice Makes Perfect</h2>
            
            <p className="text-gray-300 mb-6">
              The only way to master presentation timing is through deliberate practice with actual timing tools. 
              Here's a progressive training approach:
            </p>

            <ol className="text-gray-300 mb-8 space-y-3 ml-6">
              <li>1. <strong>Week 1:</strong> Record yourself presenting and analyze timing patterns</li>
              <li>2. <strong>Week 2:</strong> Practice with a visible countdown timer</li>
              <li>3. <strong>Week 3:</strong> Add segment-based timing for different sections</li>
              <li>4. <strong>Week 4:</strong> Simulate real presentation conditions with Q&A timing</li>
            </ol>

            <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-8 mb-8">
              <h3 className="text-2xl font-bold mb-4 text-white">Ready to Master Your Presentation Timing?</h3>
              <p className="text-gray-300 mb-6">
                TimedFlow provides all the professional timing tools you need to deliver perfectly paced presentations. 
                From remote-controlled timers to segment-based timing, we've got you covered.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  href="/signup"
                  className="px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-bold rounded-xl inline-flex items-center justify-center hover:scale-105 transition-transform"
                >
                  Start Free Trial
                </Link>
                <Link 
                  href="/demo"
                  className="px-8 py-4 border border-white/20 text-white rounded-xl hover:bg-white/5 transition-colors inline-flex items-center justify-center"
                >
                  Try Demo
                </Link>
              </div>
            </div>

            <h2 className="text-3xl font-bold mb-6 text-white">Key Takeaways</h2>
            
            <div className="bg-white/5 border border-white/10 rounded-2xl p-8 mb-8">
              <ul className="space-y-3 text-gray-300">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  Plan for 70% content, 30% interaction and transitions
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  Always practice with the actual timing tools you'll use
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  Build flexibility into your content with the accordion technique
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  Use strategic pauses to maintain control and impact
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  Make timing visible to both you and your audience when appropriate
                </li>
              </ul>
            </div>

            <p className="text-lg font-semibold text-blue-400">
              Remember: Great presentations aren&apos;t just about great content—they&apos;re about delivering that content 
              with perfect timing. Master these techniques, and you'll join the ranks of truly professional speakers.
            </p>
          </motion.div>
        </div>
      </article>

      {/* Footer */}
      <footer className="py-12 px-6 border-t border-white/10 bg-black">
        <div className="container mx-auto max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <Link href="/" className="flex items-center space-x-3 mb-6 md:mb-0">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-5 h-5 text-black" />
                </div>
              </div>
              <span className="font-bold text-lg bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </Link>
            
            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-400">
              {['Privacy Policy', 'Terms of Service', 'Support', 'API Docs'].map((item) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase().replace(/\s+/g, '-')}`}
                  className="hover:text-white transition-colors"
                >
                  {item}
                </a>
              ))}
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-white/10 text-center text-gray-500 text-sm">
            <p>© 2025 TimedFlow. Built for creators, by creators.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}