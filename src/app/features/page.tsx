"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion, useScroll, useTransform } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { createClient } from '@/lib/supabase/client'
import { getAnalyticsStats } from '@/lib/stats'
import { 
  Sparkles, 
  Video, 
  Mic, 
  Users, 
  Monitor, 
  Zap,
  Clock,
  Brain,
  Share2,
  Bell,
  Palette,
  Globe,
  Shield,
  BarChart3,
  ArrowRight,
  Play,
  ChevronRight,
  Target,
  Layers,
  Code,
  Smartphone,
  LucideIcon
} from 'lucide-react'

interface FeatureShowcaseProps {
  title: string
  description: string
  features: string[]
  icon: LucideIcon
  gradient: string
  mockup: React.ReactNode
  reverse?: boolean
  index: number
}

function FeatureShowcase({ title, description, features, icon: Icon, gradient, mockup, reverse = false, index }: FeatureShowcaseProps) {
  const { ref, inView } = useInView({
    threshold: 0.2,
    triggerOnce: true
  })

  return (
    <motion.section
      ref={ref}
      className="py-20"
    >
      <div className="container mx-auto max-w-7xl px-6">
        <div className={`grid lg:grid-cols-2 gap-12 items-center ${reverse ? 'lg:grid-flow-col-dense' : ''}`}>
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: reverse ? 50 : -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className={reverse ? 'lg:col-start-2' : ''}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ delay: 0.1 }}
              className={`inline-flex p-3 rounded-2xl bg-gradient-to-br ${gradient} mb-6`}
            >
              <Icon className="w-8 h-8 text-white" />
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.3 }}
              className="text-4xl lg:text-5xl font-bold text-white mb-6"
            >
              {title}
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.4 }}
              className="text-xl text-gray-300 mb-8 leading-relaxed"
            >
              {description}
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.5 }}
              className="space-y-4 mb-8"
            >
              {features.map((feature, i) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: -20 }}
                  animate={inView ? { opacity: 1, x: 0 } : {}}
                  transition={{ delay: 0.6 + i * 0.1 }}
                  className="flex items-center gap-3"
                >
                  <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${gradient}`} />
                  <span className="text-gray-300">{feature}</span>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.8 }}
            >
              <Link href="/signup">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`px-6 py-3 bg-gradient-to-r ${gradient} text-white font-medium rounded-xl flex items-center gap-2 group`}
                >
                  Try This Feature
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition" />
                </motion.button>
              </Link>
            </motion.div>
          </motion.div>

          {/* Mockup */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={inView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className={reverse ? 'lg:col-start-1' : ''}
          >
            {mockup}
          </motion.div>
        </div>
      </div>
    </motion.section>
  )
}

function AITimerMockup() {
  return (
    <div className="relative">
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-3xl rounded-full" />
      <div className="relative backdrop-blur-xl bg-white/5 rounded-3xl p-8 border border-white/10">
        <div className="space-y-4">
          <div className="bg-white/5 rounded-xl p-4">
            <div className="text-sm text-gray-400 mb-2">Natural Language Input:</div>
            <div className="text-white font-mono">
              &quot;30 minute podcast with 5 min intro, 20 min interview, 5 min outro&quot;
            </div>
          </div>
          
          <motion.div
            animate={{ scale: [1, 1.02, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl p-4 border border-purple-500/30"
          >
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="w-4 h-4 text-purple-400" />
              <span className="text-sm text-purple-400">AI Processing...</span>
            </div>
            
            <div className="space-y-2">
              {[
                { name: "Intro & Sponsors", time: "5:00", color: "bg-blue-500" },
                { name: "Main Interview", time: "20:00", color: "bg-green-500" },
                { name: "Outro & CTA", time: "5:00", color: "bg-purple-500" }
              ].map((segment, i) => (
                <motion.div
                  key={segment.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: i * 0.2 }}
                  className="flex items-center justify-between bg-white/5 rounded-lg p-3"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${segment.color}`} />
                    <span className="text-white text-sm">{segment.name}</span>
                  </div>
                  <span className="text-gray-400 font-mono text-sm">{segment.time}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

function CollaborationMockup() {
  const [activeUser, setActiveUser] = useState(0)
  const users = ['Sarah', 'Mike', 'Alex']

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 blur-3xl rounded-full" />
      <div className="relative backdrop-blur-xl bg-white/5 rounded-3xl p-8 border border-white/10">
        <div className="text-center mb-6">
          <div className="font-mono text-5xl font-bold text-white mb-2">
            12:45
          </div>
          <div className="text-gray-400">Main Presentation</div>
        </div>

        <div className="space-y-3 mb-6">
          {users.map((user, i) => (
            <motion.div
              key={user}
              animate={{ 
                scale: activeUser === i ? 1.02 : 1,
                backgroundColor: activeUser === i ? 'rgba(34, 197, 94, 0.2)' : 'rgba(255, 255, 255, 0.05)'
              }}
              onClick={() => setActiveUser(i)}
              className="flex items-center gap-3 p-3 rounded-xl cursor-pointer transition-all"
            >
              <div className={`w-3 h-3 rounded-full ${
                activeUser === i ? 'bg-green-500' : 'bg-gray-500'
              }`} />
              <span className="text-white text-sm">{user}</span>
              <span className="text-xs text-gray-500 ml-auto">
                {activeUser === i ? 'Controlling' : 'Viewing'}
              </span>
            </motion.div>
          ))}
        </div>

        <div className="flex gap-2">
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg p-2 text-center text-black font-medium text-sm"
          >
            Share Link
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex-1 bg-white/10 rounded-lg p-2 text-center text-white font-medium text-sm"
          >
            QR Code
          </motion.div>
        </div>
      </div>
    </div>
  )
}

function AnalyticsMockup() {
  const [analyticsStats, setAnalyticsStats] = useState({
    sessions: 0,
    watchTime: '0h',
    accuracy: 0,
    users: 0
  })
  const supabase = createClient()

  useEffect(() => {
    async function fetchStats() {
      const { data: userData } = await supabase.auth.getUser()
      if (userData.user) {
        const stats = await getAnalyticsStats(userData.user.id)
        setAnalyticsStats(stats)
      }
    }
    fetchStats()
  }, [supabase])

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 blur-3xl rounded-full" />
      <div className="relative backdrop-blur-xl bg-white/5 rounded-3xl p-8 border border-white/10">
        <div className="grid grid-cols-2 gap-4 mb-6">
          {[
            { label: "Sessions", value: analyticsStats.sessions.toLocaleString(), change: "+12%" },
            { label: "Watch Time", value: analyticsStats.watchTime, change: "+8%" },
            { label: "Accuracy", value: `${analyticsStats.accuracy}%`, change: "+2%" },
            { label: "Users", value: analyticsStats.users.toString(), change: "+24%" }
          ].map((stat, i) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: i * 0.1 }}
              className="bg-white/5 rounded-xl p-4 text-center"
            >
              <div className="text-2xl font-bold text-white">{stat.value}</div>
              <div className="text-sm text-gray-400 mb-1">{stat.label}</div>
              <div className="text-xs text-green-500">{stat.change}</div>
            </motion.div>
          ))}
        </div>

        <div className="bg-white/5 rounded-xl p-4">
          <div className="text-sm text-gray-400 mb-3">Usage Pattern</div>
          <div className="flex items-end gap-2 h-16">
            {[40, 65, 80, 45, 90, 70, 85].map((height, i) => (
              <motion.div
                key={i}
                initial={{ height: 0 }}
                animate={{ height: `${height}%` }}
                transition={{ delay: i * 0.1, duration: 0.5 }}
                className="flex-1 bg-gradient-to-t from-green-500 to-emerald-500 rounded-sm"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function FeaturesPage() {
  const [user, setUser] = useState<any>(null)
  const { scrollY } = useScroll()
  const y1 = useTransform(scrollY, [0, 1000], [0, -100])
  const y2 = useTransform(scrollY, [0, 1000], [0, -200])
  const supabase = createClient()

  // Check authentication status
  useEffect(() => {
    const checkUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        setUser(user)
      } catch (error) {
        console.error('Error checking auth:', error)
      }
    }

    checkUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase])

  const categoryFeatures = [
    {
      category: "Content Creation",
      icon: Video,
      gradient: "from-red-500 to-orange-500",
      features: [
        { icon: Video, title: "YouTube Optimization", desc: "Perfect timing for hooks, tutorials, and CTAs" },
        { icon: Mic, title: "Podcast Templates", desc: "Structured timing for interviews and discussions" },
        { icon: Target, title: "Engagement Analytics", desc: "Track timing effectiveness and audience retention" },
      ]
    },
    {
      category: "AI & Automation",
      icon: Brain,
      gradient: "from-purple-500 to-pink-500",
      features: [
        { icon: Brain, title: "Natural Language Processing", desc: "Create timers just by describing what you need" },
        { icon: Zap, title: "Smart Suggestions", desc: "AI recommends optimal timing based on content type" },
        { icon: Bell, title: "Intelligent Alerts", desc: "Context-aware notifications and cues" },
      ]
    },
    {
      category: "Team & Collaboration",
      icon: Users,
      gradient: "from-blue-500 to-cyan-500",
      features: [
        { icon: Share2, title: "Real-time Sync", desc: "Keep everyone on the same page across all devices" },
        { icon: Users, title: "Role Management", desc: "Control who can view, edit, or manage timers" },
        { icon: Globe, title: "Global Access", desc: "Work from anywhere with cloud synchronization" },
      ]
    },
    {
      category: "Professional Tools",
      icon: Monitor,
      gradient: "from-green-500 to-emerald-500",
      features: [
        { icon: Monitor, title: "Confidence Monitor", desc: "Display subtitles and notes for speakers" },
        { icon: Palette, title: "Custom Branding", desc: "Match your brand colors and fonts" },
        { icon: Code, title: "API Integration", desc: "Connect with your existing workflow tools" },
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-950 to-black" />
        <motion.div
          style={{ y: y1 }}
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-full blur-3xl"
        />
        <motion.div
          style={{ y: y2 }}
          className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"
        />
      </div>

      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 backdrop-blur-2xl bg-black/50 border-b border-white/10">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-6 h-6 text-black" />
                </div>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </Link>
            
            <div className="flex gap-4">
              <Link href="/pricing">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  className="px-4 py-2 text-gray-300 hover:text-white transition"
                >
                  Pricing
                </motion.button>
              </Link>
              {user ? (
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl"
                  >
                    Dashboard
                  </motion.button>
                </Link>
              ) : (
                <div className="flex items-center space-x-3">
                  <Link href="/login">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      className="px-4 py-2 text-gray-300 hover:text-white transition"
                    >
                      Login
                    </motion.button>
                  </Link>
                  <Link href="/signup">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl"
                    >
                      Get Started
                    </motion.button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      <div className="relative z-10">
        {/* Hero Section */}
        <section className="pt-32 pb-20 px-6">
          <div className="container mx-auto max-w-6xl text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-8"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center space-x-2 bg-white/5 backdrop-blur-xl border border-white/10 px-4 py-2 rounded-full"
              >
                <Layers className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium">Powerful Features for Every Creator</span>
              </motion.div>

              <h1 className="text-5xl lg:text-7xl font-bold">
                Everything You Need to
                <br />
                <span className="bg-gradient-to-r from-green-500 via-emerald-500 to-green-400 bg-clip-text text-transparent">
                  Master Timing
                </span>
              </h1>

              <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                From AI-powered timer creation to real-time collaboration, TimedFlow 
                provides all the tools professional creators need to deliver perfectly timed content.
              </p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-4 justify-center"
              >
                <Link href="/signup">
                  <motion.button
                    whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0, 220, 130, 0.3)" }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-bold rounded-2xl flex items-center justify-center gap-2 group"
                  >
                    Start Creating
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition" />
                  </motion.button>
                </Link>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-8 py-4 border border-white/20 hover:bg-white/5 rounded-2xl flex items-center justify-center gap-2 backdrop-blur-xl"
                >
                  <Play className="w-5 h-5" />
                  Watch Demo
                </motion.button>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Feature Categories */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-7xl">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold mb-6">
                Features by <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">Category</span>
              </h2>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8">
              {categoryFeatures.map((category, index) => {
                const CategoryIcon = category.icon
                return (
                  <motion.div
                    key={category.category}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1 }}
                    className="backdrop-blur-xl bg-white/5 rounded-3xl p-8 border border-white/10 hover:border-white/20 transition-all group"
                  >
                    <div className="flex items-center gap-4 mb-6">
                      <div className={`p-3 rounded-2xl bg-gradient-to-br ${category.gradient}`}>
                        <CategoryIcon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-white">{category.category}</h3>
                    </div>

                    <div className="space-y-4">
                      {category.features.map((feature, i) => {
                        const FeatureIcon = feature.icon
                        return (
                          <motion.div
                            key={feature.title}
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            viewport={{ once: true }}
                            transition={{ delay: index * 0.1 + i * 0.1 }}
                            className="flex items-start gap-4 p-4 rounded-xl hover:bg-white/5 transition group-hover:bg-white/5"
                          >
                            <div className={`p-2 rounded-lg bg-gradient-to-br ${category.gradient} opacity-80`}>
                              <FeatureIcon className="w-4 h-4 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-white mb-1">{feature.title}</h4>
                              <p className="text-gray-400 text-sm">{feature.desc}</p>
                            </div>
                          </motion.div>
                        )
                      })}
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </section>

        {/* Feature Showcases */}
        <FeatureShowcase
          title="AI-Powered Timer Creation"
          description="Transform natural language into perfectly structured timer sequences. Just describe what you need, and our AI creates the perfect timing breakdown for your content."
          features={[
            "Natural language processing understands context",
            "Smart suggestions based on content type",
            "Automatic segment optimization",
            "Template generation from descriptions"
          ]}
          icon={Sparkles}
          gradient="from-purple-500 to-pink-500"
          mockup={<AITimerMockup />}
          index={0}
        />

        <FeatureShowcase
          title="Real-time Collaboration"
          description="Work seamlessly with your team. Share timers, sync across devices, and give everyone the perfect view of your event timeline."
          features={[
            "Instant synchronization across all devices",
            "Role-based permissions and access control",
            "Live cursor tracking and user presence",
            "QR codes for quick sharing"
          ]}
          icon={Users}
          gradient="from-blue-500 to-cyan-500"
          mockup={<CollaborationMockup />}
          reverse
          index={1}
        />

        <FeatureShowcase
          title="Advanced Analytics"
          description="Gain insights into your timing patterns, track performance, and optimize your content delivery with detailed analytics and reporting."
          features={[
            "Detailed timing accuracy reports",
            "Usage patterns and trends",
            "Performance optimization suggestions",
            "Export data for further analysis"
          ]}
          icon={BarChart3}
          gradient="from-green-500 to-emerald-500"
          mockup={<AnalyticsMockup />}
          index={2}
        />

        {/* CTA Section */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              className="text-center backdrop-blur-2xl bg-white/5 rounded-3xl p-12 border border-white/10"
            >
              <h2 className="text-4xl font-bold mb-6">
                Ready to Experience These
                <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent"> Features</span>?
              </h2>
              <p className="text-xl text-gray-400 mb-8">
                Try 5 minute sessions for free, then unlock unlimited access for just $10/month.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/signup">
                  <motion.button
                    whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0, 220, 130, 0.3)" }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-bold rounded-2xl flex items-center justify-center gap-2 group"
                  >
                    Try 5 minutes free • $10/month Pro
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition" />
                  </motion.button>
                </Link>
                <Link href="/pricing">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-4 border border-white/20 hover:bg-white/5 rounded-2xl backdrop-blur-xl"
                  >
                    View Pricing
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </div>
  )
}