"use client"

import { Suspense, useState, useEffect } from 'react'
import Link from 'next/link'
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { createClient } from '@/lib/supabase/client'
import { 
  ArrowRight, 
  Clock, 
  Sparkles, 
  Users, 
  Zap, 
  Monitor, 
  MessageSquare,
  Timer,
  Mic,
  Video,
  Cpu,
  Globe,
  Smartphone,
  Star,
  Play,
  ChevronDown,
  Menu,
  X,
  Check,
  Presentation
} from 'lucide-react'
import FeatureCard from '@/components/landing/feature-card'
import AnimatedUserTypes from '@/components/landing/animated-user-types'

// Hero 3D component (lazy loaded)
const Hero3D = () => {
  const [mounted, setMounted] = useState(false)
  
  useEffect(() => {
    setMounted(true)
  }, [])
  
  if (!mounted) {
    return (
      <div className="h-64 w-full flex items-center justify-center">
        <div className="text-6xl font-bold text-white/20 animate-pulse">TIMED</div>
      </div>
    )
  }
  
  return (
    <Suspense fallback={
      <div className="h-64 w-full flex items-center justify-center">
        <div className="text-6xl font-bold text-white/20 animate-pulse">TIMED</div>
      </div>
    }>
      <div className="h-64 w-full flex items-center justify-center">
        <div className="text-6xl font-bold bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">
          TIMED
        </div>
      </div>
    </Suspense>
  )
}

export default function LandingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [user, setUser] = useState<any>(null)
  const { scrollY } = useScroll()
  const headerOpacity = useTransform(scrollY, [0, 100], [0.95, 1])
  const headerBlur = useTransform(scrollY, [0, 100], [10, 20])
  const supabase = createClient()

  // Floating elements parallax
  const y1 = useTransform(scrollY, [0, 1000], [0, -100])
  const y2 = useTransform(scrollY, [0, 1000], [0, -200])

  // Check authentication status
  useEffect(() => {
    const checkUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        setUser(user)
      } catch (error) {
        console.error('Error checking auth:', error)
      }
    }

    checkUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase])

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "TimedFlow",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser",
            "description": "Professional countdown timer and event management platform. Perfect for presentations, meetings, livestreams, and events with affordable pricing.",
            "url": "https://timedflow.com",
            "offers": [
              {
                "@type": "Offer",
                "name": "Free Plan",
                "price": "0",
                "priceCurrency": "USD",
                "description": "Free plan with 5-minute timer limit"
              },
              {
                "@type": "Offer", 
                "name": "Pro Plan",
                "price": "10",
                "priceCurrency": "USD",
                "billingDuration": "P1M",
                "description": "Professional plan with unlimited features"
              }
            ],
            "featureList": [
              "Remote countdown timer",
              "Multi-device synchronization", 
              "Real-time collaboration",
              "Meeting templates",
              "Presentation timing",
              "Livestream integration",
              "Event management"
            ],
            "author": {
              "@type": "Organization",
              "name": "TimedFlow"
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "ratingCount": "150"
            }
          })
        }}
      />
      
      <div className="min-h-screen bg-black text-white overflow-x-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-950 to-black" />
        {/* Floating orbs */}
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full mix-blend-screen filter blur-3xl opacity-20"
            style={{
              background: `radial-gradient(circle, ${
                i % 3 === 0 ? '#00DC82' : i % 3 === 1 ? '#FF0080' : '#00B4D8'
              } 0%, transparent 70%)`,
              width: `${300 + i * 100}px`,
              height: `${300 + i * 100}px`,
            }}
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 15 + i * 5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            initial={{
              left: `${i * 25}%`,
              top: `${i * 15}%`,
            }}
          />
        ))}
      </div>

      {/* Navigation */}
      <motion.nav 
        className="fixed top-0 w-full z-50 border-b border-white/10"
        style={{ 
          backdropFilter: `blur(${headerBlur}px)`,
          backgroundColor: `rgba(0, 0, 0, ${headerOpacity})`,
        }}
      >
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <motion.div 
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.05 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-6 h-6 text-black" />
                </div>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </motion.div>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              {['Features', 'Pricing'].map((item) => (
                <motion.a
                  key={item}
                  href={`#${item.toLowerCase()}`}
                  className="text-gray-300 hover:text-white transition-colors relative"
                  whileHover={{ y: -2 }}
                >
                  {item}
                  <motion.div
                    className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-green-500 to-emerald-500 opacity-0"
                    whileHover={{ opacity: 1 }}
                  />
                </motion.a>
              ))}
              <div className="flex items-center space-x-3">
                {user ? (
                  <Link href="/dashboard">
                    <motion.button
                      whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(0, 220, 130, 0.3)" }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl transition"
                    >
                      Dashboard
                    </motion.button>
                  </Link>
                ) : (
                  <>
                    <Link href="/login">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-4 py-2 text-gray-300 hover:text-white transition"
                      >
                        Login
                      </motion.button>
                    </Link>
                    <Link href="/signup">
                      <motion.button
                        whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(0, 220, 130, 0.3)" }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl transition"
                      >
                        Get Started
                      </motion.button>
                    </Link>
                  </>
                )}
              </div>
            </div>

            {/* Mobile Menu Button */}
            <motion.button
              className="md:hidden p-2"
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </motion.button>
          </div>

          {/* Mobile Menu */}
          <AnimatePresence>
            {isMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="md:hidden mt-4 space-y-4 pb-4"
              >
                {['Features', 'Pricing'].map((item) => (
                  <motion.a
                    key={item}
                    href={`#${item.toLowerCase()}`}
                    className="block text-gray-300 hover:text-white transition-colors"
                    whileHover={{ x: 5 }}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item}
                  </motion.a>
                ))}
                <div className="flex flex-col space-y-3 pt-4">
                  {user ? (
                    <Link href="/dashboard">
                      <button className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl w-full">
                        Dashboard
                      </button>
                    </Link>
                  ) : (
                    <>
                      <Link href="/login">
                        <button className="w-full text-left text-gray-300 hover:text-white transition">
                          Login
                        </button>
                      </Link>
                      <Link href="/signup">
                        <button className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl">
                          Get Started
                        </button>
                      </Link>
                    </>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section className="relative pt-32 pb-20 px-6 min-h-screen flex items-center">
        <div className="container mx-auto max-w-7xl relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: [0.6, -0.05, 0.01, 0.99] }}
              className="space-y-8"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center space-x-2 bg-white/5 backdrop-blur-xl border border-white/10 px-4 py-2 rounded-full"
              >
                <Sparkles className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium">Try 5 minutes Free • $10/month Pro</span>
              </motion.div>
              
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-5xl lg:text-7xl font-bold leading-tight"
              >
                Create Perfect
                <br />
                <span className="bg-gradient-to-r from-green-500 via-emerald-500 to-green-400 bg-clip-text text-transparent">
                  Timed Events
                </span>
                <br />
                Effortlessly
              </motion.h1>
              
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-xl text-gray-300 leading-relaxed max-w-xl"
              >
                Professional timers for everyone - from team standups to TED talks, 
                from podcasts to presentations. Just describe what you need, and our 
                AI creates the perfect timer sequence for any occasion.
              </motion.p>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <Link href="/r/generate">
                  <motion.button
                    whileHover={{ 
                      scale: 1.05,
                      boxShadow: "0 20px 40px rgba(0, 220, 130, 0.3)"
                    }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-bold rounded-2xl flex items-center justify-center gap-2 group"
                  >
                    Try Free Now
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition" />
                  </motion.button>
                </Link>
                <Link href="/signup">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-4 border border-white/20 hover:bg-white/5 rounded-2xl flex items-center justify-center gap-2 backdrop-blur-xl"
                  >
                    <Play className="w-5 h-5" />
                    Sign Up - $10/mo
                  </motion.button>
                </Link>
              </motion.div>

              {/* Key Benefits */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="flex items-center space-x-8 pt-8"
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">
                    Free
                  </div>
                  <div className="text-sm text-gray-400">To Get Started</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">
                    $10
                  </div>
                  <div className="text-sm text-gray-400">Per Month Pro</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">
                    Unlimited
                  </div>
                  <div className="text-sm text-gray-400">Room Creation</div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Content - 3D Hero */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="relative"
            >
              <div className="relative">
                {/* Glowing background */}
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 blur-3xl rounded-full scale-150" />
                
                {/* 3D Timer Display */}
                <div className="relative backdrop-blur-2xl bg-white/5 rounded-3xl p-8 border border-white/10">
                  <Hero3D />
                  
                  {/* Timer Display */}
                  <motion.div
                    className="text-center mt-8"
                    animate={{ 
                      scale: [1, 1.02, 1],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <div className="font-mono text-6xl lg:text-8xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-4">
                      15:32
                    </div>
                    <div className="text-gray-400 mb-2">YouTube Tutorial - Main Content</div>
                    <div className="flex items-center justify-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                        <span className="text-sm text-gray-500">Live</span>
                      </div>
                      <div className="text-sm text-gray-500">3 of 5 segments</div>
                    </div>
                  </motion.div>
                </div>

                {/* Floating Elements */}
                <motion.div
                  style={{ y: y1 }}
                  className="absolute -top-10 -left-10 w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50"
                />
                <motion.div
                  style={{ y: y2 }}
                  className="absolute -bottom-10 -right-10 w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full blur-lg opacity-50"
                />
              </div>
            </motion.div>
          </div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="flex flex-col items-center space-y-2 text-gray-500"
            >
              <span className="text-sm">Scroll to explore</span>
              <ChevronDown className="w-5 h-5" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-6 relative">
        <div className="container mx-auto max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl font-bold mb-6">
              Built for <AnimatedUserTypes />
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Perfect timing for every scenario - from daily standups to TED talks, from podcasts to team meetings.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              title="AI Timer Creation"
              description="Describe any timing need: &apos;15 minute standup&apos;, &apos;45 minute workshop with breaks&apos;, or &apos;2 hour meeting with agenda items&apos;."
              icon={Sparkles}
              gradient="from-purple-500 to-pink-500"
              delay={0.1}
            />
            <FeatureCard
              title="Universal Templates"
              description="Ready-made timers for everything - standups, TED talks, meetings, podcasts, videos, workshops, and more."
              icon={Video}
              gradient="from-red-500 to-orange-500"
              delay={0.2}
            />
            <FeatureCard
              title="Team Collaboration"
              description="Perfect for remote teams. Share timers for Google Meets, Zoom calls, or any virtual gathering."
              icon={Users}
              gradient="from-blue-500 to-cyan-500"
              delay={0.3}
            />
            <FeatureCard
              title="Real-time Sync"
              description="Everyone sees the same timer. Great for distributed teams, virtual events, or classroom activities."
              icon={Globe}
              gradient="from-green-500 to-emerald-500"
              delay={0.4}
            />
            <FeatureCard
              title="Flexible Timing"
              description="Countdown, count up, or time of day. Pause, skip, or extend segments on the fly. Full control."
              icon={Clock}
              gradient="from-indigo-500 to-purple-500"
              delay={0.5}
            />
            <FeatureCard
              title="Works Anywhere"
              description="Web-based and responsive. Use on your phone, tablet, laptop, or presentation screen."
              icon={Smartphone}
              gradient="from-yellow-500 to-orange-500"
              delay={0.6}
            />
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 px-6 relative">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl font-bold mb-6">
              Loved by <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">Professionals</span>
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Chen",
                role: "Engineering Manager, Tech Startup",
                content: "Our daily standups run perfectly now. Everyone knows when to wrap up, and meetings actually end on time. Team productivity has soared.",
                rating: 5
              },
              {
                name: "Marcus Rodriguez",
                role: "TED Talk Speaker & Coach",
                content: "The timer keeps me perfectly paced during talks. I can focus on connecting with my audience instead of watching the clock.",
                rating: 5
              },
              {
                name: "Lisa Thompson",
                role: "University Professor",
                content: "Game-changer for lectures and workshops. Students stay engaged when they can see segment timers, and I never run over anymore.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="backdrop-blur-xl bg-white/5 rounded-2xl p-8 border border-white/10"
              >
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-gray-300 mb-6 leading-relaxed">&quot;{testimonial.content}&quot;</p>
                <div>
                  <div className="font-semibold text-white">{testimonial.name}</div>
                  <div className="text-sm text-gray-500">{testimonial.role}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section id="use-cases" className="py-20 px-6 relative">
        <div className="container mx-auto max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl font-bold mb-6">
              Perfect for Every <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">Scenario</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              From daily standups to TED talks, from virtual meetings to content creation - we&apos;ve got you covered
            </p>
          </motion.div>

          {/* Use Cases Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {/* Podcast Recording */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="group relative"
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-purple-500/30 transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500">
                    <Mic className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-xs text-purple-400 font-medium">PODCASTERS</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Podcast Recording</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Keep interviews on track with segments for intro, main content, Q&A, and outros. Perfect pacing every episode.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-purple-500/10 rounded text-xs text-purple-400">30-60 min episodes</span>
                  <span className="px-2 py-1 bg-purple-500/10 rounded text-xs text-purple-400">Multi-segment</span>
                </div>
              </div>
            </motion.div>

            {/* YouTube Videos */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="group relative"
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-red-500/20 to-orange-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-red-500/30 transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-red-500 to-orange-500">
                    <Video className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-xs text-red-400 font-medium">YOUTUBERS</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">YouTube Creation</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Structure videos with hooks, tutorials, and CTAs. Hit all the right beats for maximum engagement.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-red-500/10 rounded text-xs text-red-400">10-20 min videos</span>
                  <span className="px-2 py-1 bg-red-500/10 rounded text-xs text-red-400">Retention focused</span>
                </div>
              </div>
            </motion.div>

            {/* Live Streaming */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
              className="group relative"
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-blue-500/30 transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-500">
                    <Monitor className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-xs text-blue-400 font-medium">STREAMERS</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Live Streaming</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Schedule stream segments, breaks, and special events. Keep viewers engaged with perfect timing.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-blue-500/10 rounded text-xs text-blue-400">2-4 hour streams</span>
                  <span className="px-2 py-1 bg-blue-500/10 rounded text-xs text-blue-400">Break reminders</span>
                </div>
              </div>
            </motion.div>

            {/* Presentations */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="group relative"
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-green-500/30 transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-500">
                    <Presentation className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-xs text-green-400 font-medium">SPEAKERS</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Presentations</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Deliver perfectly timed presentations with sections for intro, main points, demos, and Q&A.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-green-500/10 rounded text-xs text-green-400">15-45 min talks</span>
                  <span className="px-2 py-1 bg-green-500/10 rounded text-xs text-green-400">Q&A timing</span>
                </div>
              </div>
            </motion.div>

            {/* Meetings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5 }}
              className="group relative"
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-indigo-500/30 transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-500">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-xs text-indigo-400 font-medium">TEAMS</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Team Meetings</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Run efficient meetings with time for updates, discussion, and action items. No more overruns.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-indigo-500/10 rounded text-xs text-indigo-400">15-60 min meetings</span>
                  <span className="px-2 py-1 bg-indigo-500/10 rounded text-xs text-indigo-400">Agenda tracking</span>
                </div>
              </div>
            </motion.div>

            {/* Workshops */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6 }}
              className="group relative"
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-yellow-500/20 to-orange-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />
              <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-yellow-500/30 transition-all duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-500">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-xs text-yellow-400 font-medium">EDUCATORS</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Workshops</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Structure workshops with teaching segments, activities, breaks, and wrap-up discussions.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-yellow-500/10 rounded text-xs text-yellow-400">2-4 hour sessions</span>
                  <span className="px-2 py-1 bg-yellow-500/10 rounded text-xs text-yellow-400">Break scheduling</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Bottom CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.7 }}
            className="text-center"
          >
            <p className="text-gray-400 mb-6">
              And many more use cases including webinars, coaching sessions, study timers, and event production
            </p>
            <Link href="/dashboard/templates">
              <motion.button
                whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0, 220, 130, 0.3)" }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-bold rounded-2xl inline-flex items-center gap-2 group"
              >
                Explore All Templates
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition" />
              </motion.button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-6 relative">
        <div className="container mx-auto max-w-6xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl font-bold mb-6">
              Simple, Transparent <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">Pricing</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Start free, upgrade when you need more power
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Plan */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="backdrop-blur-xl bg-white/5 rounded-3xl p-8 border border-white/10"
            >
              <div className="mb-8">
                <h3 className="text-2xl font-bold mb-2">Free Plan</h3>
                <p className="text-gray-400">Perfect for trying out our platform - 5 minute timer limit</p>
              </div>
              
              <div className="mb-8">
                <div className="text-5xl font-bold">$0</div>
                <div className="text-gray-500">per month</div>
              </div>

              <Link href="/r/generate">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full py-4 border border-white/20 hover:bg-white/5 rounded-xl font-medium transition mb-8"
                >
                  Try Now
                </motion.button>
              </Link>

              <div className="space-y-3">
                {[
                  "5 minute timer duration limit",
                  "Basic timer types",
                  "Messages & cues"
                ].map((feature) => (
                  <div key={feature} className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Pro Plan */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="backdrop-blur-xl bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-3xl p-8 border border-green-500/30 relative"
            >
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="px-4 py-1 bg-gradient-to-r from-green-500 to-emerald-500 text-black text-sm font-bold rounded-full">
                  BEST VALUE
                </div>
              </div>

              <div className="mb-8">
                <h3 className="text-2xl font-bold mb-2 flex items-center space-x-2">
                  <span>Pro</span>
                  <Sparkles className="w-5 h-5 text-green-500" />
                </h3>
                <p className="text-gray-400">Everything you need for professional events</p>
              </div>
              
              <div className="mb-8">
                <div className="text-5xl font-bold">$10</div>
                <div className="text-gray-500">per month</div>
              </div>

              <Link href="/signup">
                <motion.button
                  whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0, 220, 130, 0.3)" }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-bold rounded-xl transition mb-8"
                >
                  Start Free Trial
                </motion.button>
              </Link>

              <div className="space-y-3">
                {[
                  "Everything in Free, plus:",
                  "Unlimited timers per room",
                  "No time duration limit",
                  "Messages",
                  "Warnings on stage with Flash messages",
                  "Unlimited connected devices",
                  "AI-powered timer creation"
                ].map((feature) => (
                  <div key={feature} className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* FAQ */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mt-20 max-w-3xl mx-auto"
          >
            <h3 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h3>
            
            <div className="space-y-6">
              {[
                {
                  q: "Can I try before I buy?",
                  a: "Absolutely! Our free plan lets you test all core features with up to 3 timers and 3 connected devices per room. No credit card required."
                },
                {
                  q: "What happens to my rooms when I downgrade?",
                  a: "Your rooms and timers remain saved. You&apos;ll have access to the first 3 timers and can connect up to 3 devices. Upgrade anytime to unlock everything again."
                },
                {
                  q: "Can I cancel anytime?",
                  a: "Yes! You can cancel your subscription at any time. You&apos;ll continue to have Pro access until the end of your billing period."
                },
                {
                  q: "Do you offer team or enterprise plans?",
                  a: "Our $10/month Pro plan includes team collaboration features. For large organizations with custom needs, please contact us for enterprise pricing."
                }
              ].map((faq, index) => (
                <div key={index} className="backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10">
                  <h4 className="text-xl font-semibold mb-2">{faq.q}</h4>
                  <p className="text-gray-400">{faq.a}</p>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 relative">
        <div className="container mx-auto max-w-4xl">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            className="relative backdrop-blur-2xl bg-white/5 rounded-3xl p-12 border border-white/10 text-center overflow-hidden"
          >
            {/* Background Effects */}
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10" />
            <div className="absolute inset-0">
              <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full blur-2xl" />
              <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl" />
            </div>
            
            <div className="relative space-y-8">
              <h2 className="text-5xl font-bold">
                Ready to Create 
                <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent"> Perfect Timers</span>?
              </h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Join thousands of professionals who&apos;ve made their meetings, presentations, and events more productive.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/signup">
                  <motion.button
                    whileHover={{ 
                      scale: 1.05,
                      boxShadow: "0 20px 40px rgba(0, 220, 130, 0.4)"
                    }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-bold rounded-2xl flex items-center justify-center gap-2 group"
                  >
                    Start Free Trial
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition" />
                  </motion.button>
                </Link>
                <Link href="/signup">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-8 py-4 border border-white/20 hover:bg-white/5 rounded-2xl backdrop-blur-xl"
                  >
                    Get Started
                  </motion.button>
                </Link>
              </div>
              <p className="text-sm text-gray-500">
                3 free sessions • No credit card required • Upgrade for $10/month
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 border-t border-white/10 relative">
        <div className="container mx-auto max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <motion.div 
              className="flex items-center space-x-3 mb-6 md:mb-0"
              whileHover={{ scale: 1.05 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-5 h-5 text-black" />
                </div>
              </div>
              <span className="font-bold text-lg bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </motion.div>
            
            <div className="flex flex-wrap justify-center gap-8 text-sm text-gray-400">
              {['Privacy Policy', 'Terms of Service', 'Support', 'API Docs', 'Status'].map((item) => (
                <motion.a
                  key={item}
                  href={`#${item.toLowerCase().replace(/\s+/g, '-')}`}
                  className="hover:text-white transition-colors"
                  whileHover={{ y: -2 }}
                >
                  {item}
                </motion.a>
              ))}
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-white/10 text-center text-gray-500 text-sm">
            <p>© 2025 TimedFlow. Built for creators, by creators.</p>
          </div>
        </div>
      </footer>
    </div>
    </>
  )
}