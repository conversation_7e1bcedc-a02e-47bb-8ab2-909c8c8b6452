"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Settings, 
  Users, 
  Share2,
  Clock,
  ArrowRight,
  Sparkles,
  Video,
  Mic,
  Calendar,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Maximize2,
  Minimize2,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface TimerSegment {
  id: number
  title: string
  duration: number
  notes: string
  type: 'intro' | 'main' | 'break' | 'outro' | 'qa'
  color: string
}

const demoSegments: TimerSegment[] = [
  { id: 1, title: "Welcome & Intro", duration: 300, notes: "Welcome everyone, introduce the topic", type: 'intro', color: 'bg-blue-500' },
  { id: 2, title: "Problem Overview", duration: 480, notes: "Explain the challenge we're solving", type: 'main', color: 'bg-purple-500' },
  { id: 3, title: "Solution Demo", duration: 900, notes: "Live demonstration of the product", type: 'main', color: 'bg-green-500' },
  { id: 4, title: "Q&A Session", duration: 600, notes: "Take questions from the audience", type: 'qa', color: 'bg-orange-500' },
  { id: 5, title: "Closing Remarks", duration: 180, notes: "Thank everyone and next steps", type: 'outro', color: 'bg-red-500' }
]

const demoScenarios = [
  {
    id: 'youtube',
    name: 'YouTube Tutorial',
    icon: Video,
    gradient: 'from-red-500 to-orange-500',
    segments: [
      { title: "Hook & Intro", duration: 30, notes: "Grab attention, introduce topic" },
      { title: "Problem Setup", duration: 120, notes: "Explain what we're solving" },
      { title: "Tutorial Content", duration: 600, notes: "Step-by-step walkthrough" },
      { title: "Call to Action", duration: 30, notes: "Like, subscribe, comment" }
    ]
  },
  {
    id: 'podcast',
    name: 'Podcast Episode',
    icon: Mic,
    gradient: 'from-purple-500 to-pink-500',
    segments: [
      { title: "Intro Music", duration: 60, notes: "Theme song and sponsors" },
      { title: "Guest Introduction", duration: 180, notes: "Introduce today's guest" },
      { title: "Main Interview", duration: 1800, notes: "Core conversation" },
      { title: "Rapid Fire Q&A", duration: 300, notes: "Quick questions round" },
      { title: "Outro & Next Episode", duration: 120, notes: "Wrap up and preview" }
    ]
  },
  {
    id: 'webinar',
    name: 'Live Webinar',
    icon: Calendar,
    gradient: 'from-blue-500 to-cyan-500',
    segments: demoSegments.map(s => ({ title: s.title, duration: s.duration, notes: s.notes }))
  }
]

function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

export default function DemoPage() {
  const [selectedScenario, setSelectedScenario] = useState(demoScenarios[2])
  const [currentSegment, setCurrentSegment] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(selectedScenario.segments[0]?.duration || 300)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showNotes, setShowNotes] = useState(true)
  const [isMuted, setIsMuted] = useState(false)
  const [connectedUsers] = useState(['You', 'Sarah M.', 'Mike R.', 'Alex P.'])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isPlaying && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            // Move to next segment
            if (currentSegment < selectedScenario.segments.length - 1) {
              setCurrentSegment(prev => prev + 1)
              return selectedScenario.segments[currentSegment + 1]?.duration || 0
            } else {
              setIsPlaying(false)
              return 0
            }
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isPlaying, timeRemaining, currentSegment, selectedScenario.segments])

  const currentSegmentData = selectedScenario.segments[currentSegment]
  const totalDuration = selectedScenario.segments.reduce((sum, seg) => sum + seg.duration, 0)
  const elapsedTotal = selectedScenario.segments.slice(0, currentSegment).reduce((sum, seg) => sum + seg.duration, 0) + 
                     (currentSegmentData?.duration - timeRemaining)
  const progressPercentage = (elapsedTotal / totalDuration) * 100

  const resetTimer = () => {
    setIsPlaying(false)
    setCurrentSegment(0)
    setTimeRemaining(selectedScenario.segments[0]?.duration || 0)
  }

  const nextSegment = () => {
    if (currentSegment < selectedScenario.segments.length - 1) {
      setCurrentSegment(prev => prev + 1)
      setTimeRemaining(selectedScenario.segments[currentSegment + 1]?.duration || 0)
    }
  }

  const prevSegment = () => {
    if (currentSegment > 0) {
      setCurrentSegment(prev => prev - 1)
      setTimeRemaining(selectedScenario.segments[currentSegment - 1]?.duration || 0)
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-950 to-black" />
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full mix-blend-screen filter blur-3xl opacity-20"
            style={{
              background: `radial-gradient(circle, ${i === 0 ? '#00DC82' : i === 1 ? '#FF0080' : '#00B4D8'} 0%, transparent 70%)`,
              width: `${300 + i * 100}px`,
              height: `${300 + i * 100}px`,
            }}
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 15 + i * 5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            initial={{
              left: `${i * 30}%`,
              top: `${i * 20}%`,
            }}
          />
        ))}
      </div>

      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 backdrop-blur-2xl bg-black/50 border-b border-white/10">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg blur opacity-50" />
                <div className="relative bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-lg">
                  <Clock className="w-6 h-6 text-black" />
                </div>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                TimedFlow
              </span>
            </Link>
            
            <div className="flex gap-4">
              <Link href="/features">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  className="px-4 py-2 text-gray-300 hover:text-white transition"
                >
                  Features
                </motion.button>
              </Link>
              <Link href="/signup">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl"
                >
                  Get Started
                </motion.button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="relative z-10 pt-32 pb-20 px-6">
        <div className="container mx-auto max-w-7xl">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h1 className="text-5xl lg:text-6xl font-bold mb-6">
              Interactive
              <span className="bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent"> Demo</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Experience TimedFlow in action. Try different scenarios and see how our timer system works for various content types.
            </p>

            {/* Scenario Selector */}
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              {demoScenarios.map((scenario) => {
                const Icon = scenario.icon
                return (
                  <motion.button
                    key={scenario.id}
                    onClick={() => {
                      setSelectedScenario(scenario)
                      setCurrentSegment(0)
                      setTimeRemaining(scenario.segments[0]?.duration || 0)
                      setIsPlaying(false)
                    }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`flex items-center gap-3 px-6 py-3 rounded-2xl border transition-all ${
                      selectedScenario.id === scenario.id
                        ? `bg-gradient-to-r ${scenario.gradient} text-white border-transparent`
                        : 'bg-white/5 border-white/20 hover:border-white/40 text-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {scenario.name}
                  </motion.button>
                )
              })}
            </div>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Timer Display */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="lg:col-span-2"
            >
              <div className={`backdrop-blur-2xl bg-white/5 rounded-3xl border border-white/10 overflow-hidden ${
                isFullscreen ? 'fixed inset-4 z-50' : ''
              }`}>
                {/* Timer Header */}
                <div className="flex items-center justify-between p-6 border-b border-white/10">
                  <div className="flex items-center gap-4">
                    <div className={`p-2 rounded-lg bg-gradient-to-r ${selectedScenario.gradient}`}>
                      <selectedScenario.icon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">{selectedScenario.name}</h3>
                      <p className="text-sm text-gray-400">
                        Segment {currentSegment + 1} of {selectedScenario.segments.length}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setShowNotes(!showNotes)}
                      className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition"
                    >
                      {showNotes ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsMuted(!isMuted)}
                      className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition"
                    >
                      {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsFullscreen(!isFullscreen)}
                      className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition"
                    >
                      {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                    </motion.button>
                  </div>
                </div>

                {/* Main Timer */}
                <div className="p-12 text-center">
                  <motion.div
                    key={currentSegment}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    className="mb-8"
                  >
                    <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                      {currentSegmentData?.title}
                    </h2>
                    <div className="font-mono text-8xl lg:text-9xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-6">
                      {formatTime(timeRemaining)}
                    </div>
                    
                    {showNotes && currentSegmentData && (
                      <motion.p
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-xl text-gray-400 mb-6 max-w-2xl mx-auto"
                      >
                        {currentSegmentData.notes}
                      </motion.p>
                    )}
                  </motion.div>

                  {/* Progress Bar */}
                  <div className="mb-8">
                    <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-green-500 to-emerald-500"
                        style={{ width: `${progressPercentage}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                    <div className="flex justify-between text-sm text-gray-500 mt-2">
                      <span>{formatTime(elapsedTotal)}</span>
                      <span>{formatTime(totalDuration)}</span>
                    </div>
                  </div>

                  {/* Controls */}
                  <div className="flex items-center justify-center gap-4">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={prevSegment}
                      disabled={currentSegment === 0}
                      className="p-4 rounded-full bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition"
                    >
                      <ChevronLeft className="w-6 h-6" />
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsPlaying(!isPlaying)}
                      className="p-6 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 text-black"
                    >
                      {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={resetTimer}
                      className="p-4 rounded-full bg-white/10 hover:bg-white/20 transition"
                    >
                      <RotateCcw className="w-6 h-6" />
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={nextSegment}
                      disabled={currentSegment === selectedScenario.segments.length - 1}
                      className="p-4 rounded-full bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition"
                    >
                      <ChevronRight className="w-6 h-6" />
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              {/* Segments List */}
              <div className="backdrop-blur-xl bg-white/5 rounded-3xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Segments</h3>
                <div className="space-y-3">
                  {selectedScenario.segments.map((segment, index) => (
                    <motion.div
                      key={index}
                      className={`p-3 rounded-xl transition-all cursor-pointer ${
                        index === currentSegment
                          ? 'bg-green-500/20 border border-green-500/50'
                          : index < currentSegment
                          ? 'bg-white/5 opacity-60'
                          : 'bg-white/5 hover:bg-white/10'
                      }`}
                      onClick={() => {
                        setCurrentSegment(index)
                        setTimeRemaining(segment.duration)
                        setIsPlaying(false)
                      }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-white text-sm">{segment.title}</h4>
                          <p className="text-xs text-gray-400">{segment.notes}</p>
                        </div>
                        <span className="text-xs font-mono text-gray-400">
                          {formatTime(segment.duration)}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Connected Users */}
              <div className="backdrop-blur-xl bg-white/5 rounded-3xl p-6 border border-white/10">
                <div className="flex items-center gap-2 mb-4">
                  <Users className="w-5 h-5 text-green-500" />
                  <h3 className="text-lg font-semibold text-white">Connected Users</h3>
                </div>
                <div className="space-y-2">
                  {connectedUsers.map((user, index) => (
                    <div key={user} className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 transition">
                      <div className={`w-2 h-2 rounded-full ${index === 0 ? 'bg-green-500' : 'bg-blue-500'}`} />
                      <span className="text-sm text-gray-300">{user}</span>
                      {index === 0 && (
                        <span className="text-xs text-green-500 bg-green-500/20 px-2 py-1 rounded-full">
                          Controller
                        </span>
                      )}
                    </div>
                  ))}
                </div>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full mt-4 p-3 bg-white/10 hover:bg-white/20 rounded-xl text-sm font-medium text-white transition flex items-center justify-center gap-2"
                >
                  <Share2 className="w-4 h-4" />
                  Share Session
                </motion.button>
              </div>

              {/* Try it Yourself */}
              <div className="backdrop-blur-xl bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-3xl p-6 border border-green-500/20">
                <div className="flex items-center gap-2 mb-4">
                  <Sparkles className="w-5 h-5 text-green-500" />
                  <h3 className="text-lg font-semibold text-white">Try it Yourself</h3>
                </div>
                <p className="text-sm text-gray-300 mb-4">
                  Ready to create your own timers? Try 5 minutes free, then $10/month for unlimited access.
                </p>
                <Link href="/signup">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full p-3 bg-gradient-to-r from-green-500 to-emerald-500 text-black font-medium rounded-xl flex items-center justify-center gap-2"
                  >
                    Try 5 minutes free • $10/month Pro
                    <ArrowRight className="w-4 h-4" />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}