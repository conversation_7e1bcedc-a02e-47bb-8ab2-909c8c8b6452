import type { Metadata } from 'next'
import { Inter, JetBrains_Mono } from 'next/font/google'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const jetbrainsMono = JetBrains_Mono({ 
  subsets: ['latin'],
  variable: '--font-mono',
})

export const metadata: Metadata = {
  title: 'TimedFlow - Professional Event Timer & Countdown Platform',
  description: 'Professional countdown timer and event management platform. Perfect for presentations, meetings, livestreams, and events. Affordable $10/month pricing with free trial. Try now!',
  keywords: 'countdown timer, event timer, presentation timer, stage timer, meeting timer, livestream timer, broadcast timer, remote timer, professional timer, event management, presentation timing',
  openGraph: {
    title: 'TimedFlow - Professional Event Timer & Countdown Platform',
    description: 'Professional countdown timer and event management platform. Perfect for presentations, meetings, livestreams, and events. Try free now!',
    type: 'website',
    siteName: 'TimedFlow',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TimedFlow - Professional Event Timer Platform',
    description: 'Professional countdown timer and event management platform. Perfect for presentations, meetings, and events. Try free now!',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased min-h-screen bg-black`}>
        {children}
      </body>
    </html>
  )
} 