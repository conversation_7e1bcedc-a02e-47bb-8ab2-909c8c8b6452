'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import DashboardLayout from '@/components/dashboard/dashboard-layout'
import { 
  Sparkles, 
  Clock, 
  Mic, 
  Video, 
  Users, 
  Presentation,
  Coffee,
  Zap,
  Play
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface Template {
  id: string
  name: string
  description: string
  icon: any
  gradient: string
  duration: string
  segments: Array<{
    name: string
    duration: number
    color: string
  }>
}

const templates: Template[] = [
  {
    id: 'podcast',
    name: 'Podcast Recording',
    description: 'Perfect for recording podcast episodes with intro, content, and outro',
    icon: Mic,
    gradient: 'from-purple-500 to-pink-500',
    duration: '30 min',
    segments: [
      { name: 'Intro & Sponsors', duration: 300, color: 'bg-purple-500' },
      { name: 'Main Content', duration: 1200, color: 'bg-pink-500' },
      { name: 'Q&A Session', duration: 300, color: 'bg-purple-500' },
      { name: 'Outro & CTA', duration: 120, color: 'bg-pink-500' }
    ]
  },
  {
    id: 'youtube',
    name: 'YouTube Video',
    description: 'Structured timing for YouTube content creation',
    icon: Video,
    gradient: 'from-red-500 to-orange-500',
    duration: '15 min',
    segments: [
      { name: 'Hook', duration: 30, color: 'bg-red-500' },
      { name: 'Intro', duration: 60, color: 'bg-orange-500' },
      { name: 'Main Content', duration: 600, color: 'bg-red-500' },
      { name: 'Call to Action', duration: 60, color: 'bg-orange-500' },
      { name: 'End Screen', duration: 30, color: 'bg-red-500' }
    ]
  },
  {
    id: 'meeting',
    name: 'Team Meeting',
    description: 'Keep your meetings focused and on schedule',
    icon: Users,
    gradient: 'from-blue-500 to-cyan-500',
    duration: '45 min',
    segments: [
      { name: 'Check-in', duration: 300, color: 'bg-blue-500' },
      { name: 'Updates', duration: 900, color: 'bg-cyan-500' },
      { name: 'Discussion', duration: 1200, color: 'bg-blue-500' },
      { name: 'Action Items', duration: 300, color: 'bg-cyan-500' }
    ]
  },
  {
    id: 'presentation',
    name: 'Presentation',
    description: 'Professional presentation with Q&A',
    icon: Presentation,
    gradient: 'from-green-500 to-emerald-500',
    duration: '20 min',
    segments: [
      { name: 'Introduction', duration: 120, color: 'bg-green-500' },
      { name: 'Main Points', duration: 600, color: 'bg-emerald-500' },
      { name: 'Case Studies', duration: 300, color: 'bg-green-500' },
      { name: 'Q&A', duration: 180, color: 'bg-emerald-500' }
    ]
  },
  {
    id: 'standup',
    name: 'Daily Standup',
    description: 'Quick team sync with updates',
    icon: Zap,
    gradient: 'from-yellow-500 to-orange-500',
    duration: '15 min',
    segments: [
      { name: 'Yesterday', duration: 300, color: 'bg-yellow-500' },
      { name: 'Today', duration: 300, color: 'bg-orange-500' },
      { name: 'Blockers', duration: 180, color: 'bg-yellow-500' },
      { name: 'Quick Sync', duration: 120, color: 'bg-orange-500' }
    ]
  },
  {
    id: 'break',
    name: 'Work Break',
    description: 'Pomodoro-style work and break intervals',
    icon: Coffee,
    gradient: 'from-indigo-500 to-purple-500',
    duration: '30 min',
    segments: [
      { name: 'Focus Work', duration: 1500, color: 'bg-indigo-500' },
      { name: 'Short Break', duration: 300, color: 'bg-purple-500' }
    ]
  }
]

export default function TemplatesPage() {
  const [isCreating, setIsCreating] = useState<string | null>(null)
  const router = useRouter()

  const createFromTemplate = async (template: Template) => {
    setIsCreating(template.id)
    try {
      const response = await fetch('/api/rooms/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'template',
          name: template.name,
          timerConfig: {
            segments: template.segments
          }
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create room')
      }

      router.push(data.controllerUrl)
    } catch (error) {
      console.error('Error creating room from template:', error)
      alert('Failed to create timer. Please try again.')
    } finally {
      setIsCreating(null)
    }
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-2">
            Timer Templates
          </h1>
          <p className="text-gray-400 text-lg">
            Start with pre-built timer sequences for common use cases
          </p>
        </motion.div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template, index) => {
            const Icon = template.icon
            return (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="group relative"
              >
                {/* Glow Effect */}
                <div className={cn(
                  "absolute inset-0 rounded-2xl bg-gradient-to-r opacity-0 group-hover:opacity-10 blur-xl transition-opacity duration-500",
                  template.gradient
                )} />

                {/* Card */}
                <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={cn(
                      "p-3 rounded-xl bg-gradient-to-br",
                      template.gradient
                    )}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-sm text-gray-400">{template.duration}</span>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-white mb-2">{template.name}</h3>
                  <p className="text-gray-400 text-sm mb-4">{template.description}</p>

                  {/* Segments Preview */}
                  <div className="space-y-2 mb-4">
                    {template.segments.map((segment, i) => (
                      <div key={i} className="flex items-center gap-2">
                        <div className={cn("w-2 h-2 rounded-full", segment.color)} />
                        <span className="text-xs text-gray-500">{segment.name}</span>
                        <span className="text-xs text-gray-600 ml-auto">
                          {Math.floor(segment.duration / 60)}:{(segment.duration % 60).toString().padStart(2, '0')}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Action Button */}
                  <button
                    onClick={() => createFromTemplate(template)}
                    disabled={isCreating === template.id}
                    className={cn(
                      "w-full py-3 rounded-xl font-medium flex items-center justify-center gap-2 transition-all",
                      "bg-gradient-to-r hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",
                      template.gradient
                    )}
                  >
                    {isCreating === template.id ? (
                      <span className="text-white">Creating...</span>
                    ) : (
                      <>
                        <Play className="w-4 h-4 text-white" />
                        <span className="text-white">Use Template</span>
                      </>
                    )}
                  </button>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Custom Template CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-12 text-center"
        >
          <div className="inline-flex items-center gap-2 text-gray-400 mb-4">
            <Sparkles className="w-4 h-4" />
            <span>Need something custom?</span>
          </div>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-6 py-3 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl font-medium transition-all"
          >
            Create Custom Timer
          </button>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}

// Helper function
function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}