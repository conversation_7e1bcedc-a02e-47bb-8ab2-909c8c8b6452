'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import DashboardLayout from '@/components/dashboard/dashboard-layout'
import { 
  Mic, 
  Clock, 
  Users, 
  Music,
  MessageSquare,
  Sparkles,
  Play,
  Plus
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface PodcastTemplate {
  id: string
  name: string
  description: string
  duration: string
  gradient: string
  segments: Array<{
    name: string
    duration: number
    description: string
  }>
}

const podcastTemplates: PodcastTemplate[] = [
  {
    id: 'interview',
    name: 'Interview Format',
    description: 'One-on-one interview with guest',
    duration: '60 min',
    gradient: 'from-purple-500 to-pink-500',
    segments: [
      { name: 'Pre-show & Intro', duration: 180, description: 'Welcome listeners and introduce guest' },
      { name: 'Guest Background', duration: 600, description: 'Learn about guest\'s journey' },
      { name: 'Main Interview', duration: 1800, description: 'Deep dive into topic' },
      { name: 'Rapid Fire Q&A', duration: 300, description: 'Quick questions' },
      { name: 'Closing & CTA', duration: 120, description: 'Thank guest and promote links' }
    ]
  },
  {
    id: 'solo',
    name: 'Solo Episode',
    description: 'Educational or storytelling content',
    duration: '30 min',
    gradient: 'from-blue-500 to-cyan-500',
    segments: [
      { name: 'Hook & Intro', duration: 120, description: 'Grab attention and introduce topic' },
      { name: 'Main Content', duration: 1200, description: 'Core teaching or story' },
      { name: 'Examples/Case Studies', duration: 360, description: 'Real-world applications' },
      { name: 'Summary & Action Steps', duration: 120, description: 'Key takeaways' }
    ]
  },
  {
    id: 'panel',
    name: 'Panel Discussion',
    description: 'Multiple guests discussing a topic',
    duration: '45 min',
    gradient: 'from-green-500 to-emerald-500',
    segments: [
      { name: 'Introductions', duration: 300, description: 'Introduce all panelists' },
      { name: 'Topic 1', duration: 600, description: 'First discussion point' },
      { name: 'Topic 2', duration: 600, description: 'Second discussion point' },
      { name: 'Topic 3', duration: 600, description: 'Third discussion point' },
      { name: 'Audience Q&A', duration: 300, description: 'Questions from listeners' },
      { name: 'Wrap-up', duration: 120, description: 'Final thoughts from each panelist' }
    ]
  },
  {
    id: 'news',
    name: 'News & Updates',
    description: 'Industry news and commentary',
    duration: '20 min',
    gradient: 'from-red-500 to-orange-500',
    segments: [
      { name: 'Headlines', duration: 120, description: 'Top stories overview' },
      { name: 'Story 1 Deep Dive', duration: 300, description: 'Analysis of main story' },
      { name: 'Story 2 & 3', duration: 360, description: 'Additional news items' },
      { name: 'Community Updates', duration: 180, description: 'Listener news and shoutouts' },
      { name: 'Next Episode Preview', duration: 60, description: 'What\'s coming next' }
    ]
  },
  {
    id: 'qa',
    name: 'Q&A Episode',
    description: 'Answering listener questions',
    duration: '35 min',
    gradient: 'from-indigo-500 to-purple-500',
    segments: [
      { name: 'Intro & Format', duration: 120, description: 'Explain Q&A format' },
      { name: 'Question Block 1', duration: 600, description: '3-4 detailed answers' },
      { name: 'Quick Fire Round', duration: 300, description: 'Rapid responses' },
      { name: 'Deep Dive Question', duration: 600, description: 'One complex topic' },
      { name: 'How to Submit Qs', duration: 120, description: 'Encourage submissions' }
    ]
  }
]

export default function PodcastsPage() {
  const [isCreating, setIsCreating] = useState<string | null>(null)
  const router = useRouter()

  const createPodcastTimer = async (template: PodcastTemplate) => {
    setIsCreating(template.id)
    try {
      const response = await fetch('/api/rooms/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'template',
          name: `Podcast: ${template.name}`,
          timerConfig: {
            segments: template.segments.map(seg => ({
              name: seg.name,
              duration: seg.duration
            }))
          }
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create room')
      }

      router.push(data.controllerUrl)
    } catch (error) {
      console.error('Error creating podcast timer:', error)
      alert('Failed to create timer. Please try again.')
    } finally {
      setIsCreating(null)
    }
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500">
              <Mic className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white">
              Podcast Templates
            </h1>
          </div>
          <p className="text-gray-400 text-lg">
            Professional timer sequences designed specifically for podcast recording
          </p>
        </motion.div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-12">
          {podcastTemplates.map((template, index) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="group relative"
            >
              {/* Glow Effect */}
              <div className={cn(
                "absolute inset-0 rounded-2xl bg-gradient-to-r opacity-0 group-hover:opacity-10 blur-xl transition-opacity duration-500",
                template.gradient
              )} />

              {/* Card */}
              <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">{template.name}</h3>
                    <p className="text-gray-400 text-sm">{template.description}</p>
                  </div>
                  <span className="px-3 py-1 bg-white/10 rounded-full text-sm text-gray-300">
                    {template.duration}
                  </span>
                </div>

                {/* Segments */}
                <div className="space-y-3 mb-6">
                  {template.segments.map((segment, i) => (
                    <div key={i} className="flex items-start gap-3">
                      <div className={cn(
                        "w-1.5 h-1.5 rounded-full mt-1.5 flex-shrink-0",
                        "bg-gradient-to-r",
                        template.gradient
                      )} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-white">{segment.name}</span>
                          <span className="text-xs text-gray-500">
                            {Math.floor(segment.duration / 60)}:{(segment.duration % 60).toString().padStart(2, '0')}
                          </span>
                        </div>
                        <p className="text-xs text-gray-500">{segment.description}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Action Button */}
                <button
                  onClick={() => createPodcastTimer(template)}
                  disabled={isCreating === template.id}
                  className={cn(
                    "w-full py-3 rounded-xl font-medium flex items-center justify-center gap-2 transition-all",
                    "bg-gradient-to-r hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed text-white",
                    template.gradient
                  )}
                >
                  {isCreating === template.id ? (
                    <span>Creating...</span>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      <span>Start Recording</span>
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Tips Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl p-8 border border-purple-500/20"
        >
          <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-400" />
            Pro Podcast Tips
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start gap-3">
              <Clock className="w-4 h-4 text-purple-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">Respect the Timer</h4>
                <p className="text-sm text-gray-400">Keep segments on track to maintain listener engagement</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Users className="w-4 h-4 text-purple-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">Guest Prep</h4>
                <p className="text-sm text-gray-400">Share timer with guests so they know the flow</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Music className="w-4 h-4 text-purple-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">Audio Cues</h4>
                <p className="text-sm text-gray-400">Use timer alerts as natural transition points</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <MessageSquare className="w-4 h-4 text-purple-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">Flexible Pacing</h4>
                <p className="text-sm text-gray-400">Pause timer for important tangents, resume when ready</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Custom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-8 text-center"
        >
          <button
            onClick={() => router.push('/dashboard')}
            className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl font-medium transition-all"
          >
            <Plus className="w-4 h-4" />
            <span>Create Custom Podcast Timer</span>
          </button>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}

// Helper function
function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}