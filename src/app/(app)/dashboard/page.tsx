"use client"

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Plus, 
  Clock, 
  Play, 
  Pause,
  Trash2,
  TrendingUp,
  Users,
  Zap,
  Calendar,
  ArrowRight,
  Sparkles,
  Timer,
  Activity,
  Target,
  Award,
  ExternalLink
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/dashboard-layout'
import { createClient } from '@/lib/supabase/client'
import { Room, Timer as TimerType } from '@/types/database'
import { getDashboardStats } from '@/lib/stats'
import QuickTimerModal from '@/components/timer/quick-timer-modal'
import AITimerModal from '@/components/timer/ai-timer-modal'
import CreateRoomModal from '@/components/timer/create-room-modal'
import DeleteRoomModal from '@/components/timer/delete-room-modal'

interface RoomWithTimers extends Room {
  timers: TimerType[]
}


export default function DashboardPage() {
  const [rooms, setRooms] = useState<RoomWithTimers[]>([])
  const [loadingRooms, setLoadingRooms] = useState(true)
  const [isCreatingRoom, setIsCreatingRoom] = useState(false)
  const [stats, setStats] = useState({
    totalSessions: 0,
    activeNow: 0,
    teamMembers: 0,
    efficiency: 0
  })
  const [loadingStats, setLoadingStats] = useState(true)
  const [isQuickTimerModalOpen, setIsQuickTimerModalOpen] = useState(false)
  const [isAITimerModalOpen, setIsAITimerModalOpen] = useState(false)
  const [isCreateRoomModalOpen, setIsCreateRoomModalOpen] = useState(false)
  const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; room: RoomWithTimers | null }>({ isOpen: false, room: null })
  const [isDeletingRoom, setIsDeletingRoom] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  // Function to refresh stats
  const refreshStats = async () => {
    try {
      const { data: userData } = await supabase.auth.getUser()
      if (!userData.user) return

      const dashboardStats = await getDashboardStats(userData.user.id)
      setStats(dashboardStats)
    } catch (error) {
      console.error('Error refreshing stats:', error)
    }
  }

  // Fetch user's rooms and stats
  useEffect(() => {
    async function fetchData() {
      try {
        // Get user's rooms with timer count
        const { data: userData } = await supabase.auth.getUser()
        if (!userData.user) return

        // Fetch rooms
        const { data: roomsData, error } = await supabase
          .from('rooms')
          .select(`
            *,
            timers (*)
          `)
          .eq('owner_id', userData.user.id)
          .order('last_active', { ascending: false })
          .limit(10)

        if (error) {
          console.error('Error fetching rooms:', error)
        } else {
          setRooms(roomsData as RoomWithTimers[] || [])
        }

        // Fetch stats
        await refreshStats()
      } catch (error) {
        console.error('Error:', error)
      } finally {
        setLoadingRooms(false)
        setLoadingStats(false)
      }
    }

    fetchData()
  }, [supabase])

  // Format duration for display
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:00`
    }
    return `${mins}:00`
  }

  // Format last used time
  const formatLastUsed = (date: string) => {
    const now = new Date()
    const lastUsed = new Date(date)
    const diffMs = now.getTime() - lastUsed.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hours ago`
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    return lastUsed.toLocaleDateString()
  }

  // Get total duration of all timers in a room
  const getTotalDuration = (timers: TimerType[]) => {
    const totalSeconds = timers.reduce((acc, timer) => acc + (timer.duration || 0), 0)
    return formatDuration(totalSeconds)
  }

  // Build quick stats array from fetched data
  const quickStats = [
    { 
      label: "Total Timers", 
      value: stats.totalSessions.toString(), 
      trend: "up" as const,
      icon: Timer,
      color: "from-green-500 to-emerald-500"
    }
  ]

  // Create a new room
  const createRoom = async (type: 'manual' | 'quick' | 'ai', config?: any, name?: string, description?: string) => {
    setIsCreatingRoom(true)
    try {
      const response = await fetch('/api/rooms/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          name,
          description,
          timerConfig: config
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create room')
      }

      router.push(data.controllerUrl)
    } catch (error) {
      console.error('Error creating room:', error)
      alert('Failed to create room. Please try again.')
    } finally {
      setIsCreatingRoom(false)
    }
  }

  // Delete a room
  const deleteRoom = async (roomId: string) => {
    setIsDeletingRoom(true)
    try {
      const { error } = await supabase
        .from('rooms')
        .delete()
        .eq('id', roomId)

      if (error) {
        throw error
      }

      // Remove the room from the local state
      setRooms(rooms.filter(r => r.id !== roomId))
      
      // Refresh stats to update total sessions count
      await refreshStats()
      
      // Close the modal
      setDeleteModal({ isOpen: false, room: null })
    } catch (error) {
      console.error('Error deleting room:', error)
      alert('Failed to delete room. Please try again.')
    } finally {
      setIsDeletingRoom(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-400 text-lg">Your timers dashboard is ready. Let&apos;s make today count!</p>
        </motion.div>

        {/* Quick Stats Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          {quickStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="relative group"
              >
                {/* Glow Effect */}
                <div className={cn(
                  "absolute inset-0 rounded-2xl bg-gradient-to-r opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500",
                  stat.color
                )} />
                
                {/* Card */}
                <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300">
                  <div className="flex items-start justify-between mb-4">
                    <div className={cn(
                      "p-3 rounded-xl bg-gradient-to-br",
                      stat.color
                    )}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-1">{stat.value}</h3>
                  <p className="text-gray-500 text-sm">{stat.label}</p>
                </div>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Sessions - 2 columns */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Recent Sessions</h2>
              <Link href="/dashboard/sessions">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="text-gray-400 hover:text-white transition flex items-center gap-2"
                >
                  View all
                  <ArrowRight className="w-4 h-4" />
                </motion.button>
              </Link>
            </div>

            {loadingRooms ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-400">Loading sessions...</div>
              </div>
            ) : rooms.length === 0 ? (
              <div className="text-center py-12">
                <Clock className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-400 text-lg mb-2">No timer sessions yet</p>
                <p className="text-gray-500">Create your first timer to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {rooms.map((room, index) => {
                  const colors = [
                    "from-blue-500 to-cyan-500",
                    "from-red-500 to-orange-500",
                    "from-purple-500 to-pink-500",
                    "from-green-500 to-emerald-500"
                  ]
                  const color = colors[index % colors.length]
                  
                  return (
                    <motion.div
                      key={room.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ scale: 1.01 }}
                      className="group relative cursor-pointer"
                      onClick={() => router.push(`/r/${room.slug}/controller`)}
                    >
                      {/* Glow Effect */}
                      <div className={cn(
                        "absolute inset-0 rounded-2xl bg-gradient-to-r opacity-0 group-hover:opacity-10 blur-xl transition-opacity duration-500",
                        color
                      )} />

                      {/* Card */}
                      <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            {/* Timer Icon */}
                            <motion.div
                              whileHover={{ rotate: 360 }}
                              transition={{ duration: 0.5 }}
                              className={cn(
                                "p-3 rounded-xl bg-gradient-to-br",
                                color
                              )}
                            >
                              <Clock className="w-6 h-6 text-white" />
                            </motion.div>

                            {/* Session Info */}
                            <div>
                              <h3 className="text-lg font-semibold text-white mb-1">{room.name}</h3>
                              <div className="flex items-center gap-4 text-sm text-gray-400">
                                <span className="flex items-center gap-1">
                                  <Timer className="w-3.5 h-3.5" />
                                  {room.timers.length > 0 ? getTotalDuration(room.timers) : '0:00'}
                                </span>
                                <span>{room.timers.length} timer{room.timers.length !== 1 ? 's' : ''}</span>
                                <span className="text-gray-500">
                                  {formatLastUsed(room.last_active)}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center gap-3">
                            <Link href={`/r/${room.slug}`} target="_blank">
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={(e) => {
                                  e.stopPropagation()
                                }}
                                className="p-3 rounded-xl bg-white/5 text-white hover:bg-white/10 transition-all duration-300"
                                title="Open viewer"
                              >
                                <ExternalLink className="w-5 h-5" />
                              </motion.button>
                            </Link>
                            <motion.button 
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={(e) => {
                                e.stopPropagation()
                                setDeleteModal({ isOpen: true, room })
                              }}
                              className="p-3 rounded-xl bg-red-500/10 text-red-400 hover:bg-red-500/20 hover:text-red-300 transition-all duration-300"
                              title="Delete room"
                            >
                              <Trash2 className="w-5 h-5" />
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            )}

            {/* Create New Button */}
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsCreateRoomModalOpen(true)}
              className="w-full mt-6 p-6 rounded-2xl border-2 border-dashed border-white/20 hover:border-white/40 transition-all duration-300 group"
            >
              <div className="flex items-center justify-center gap-3 text-gray-400 group-hover:text-white transition">
                <Plus className="w-5 h-5" />
                <span className="font-medium">Create New Timer</span>
              </div>
            </motion.button>
          </motion.div>

          {/* Right Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-6"
          >
            {/* AI Assistant Card */}
            <div className="backdrop-blur-xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-2xl p-6 border border-purple-500/20">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white">AI Assistant</h3>
              </div>
              <p className="text-gray-400 text-sm mb-4">
                Create complex timer sequences with natural language. Try: &quot;30 minute podcast with 5 min intro, 20 min interview, 5 min outro&quot;
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsAITimerModalOpen(true)}
                className="w-full py-3 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium hover:shadow-lg hover:shadow-purple-500/25 transition"
              >
                Try AI Timer
              </motion.button>
            </div>

            {/* Quick Actions */}
            <div className="backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                {[
                  { icon: Timer, label: "Quick Timer", color: "from-blue-500 to-cyan-500", action: () => setIsQuickTimerModalOpen(true) },
                  { icon: Clock, label: "5 Min Timer", color: "from-green-500 to-emerald-500", action: () => createRoom('quick', { name: '5 Minute Timer', duration: 300 }, '5 Minute Timer') },
                ].map((action, index) => {
                  const Icon = action.icon
                  return (
                    <motion.button
                      key={action.label}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                      whileHover={{ scale: 1.02, x: 5 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={action.action}
                      disabled={isCreatingRoom}
                      className="w-full flex items-center gap-3 p-3 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div className={cn(
                        "p-2 rounded-lg bg-gradient-to-br",
                        action.color
                      )}>
                        <Icon className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-white font-medium">{action.label}</span>
                    </motion.button>
                  )
                })}
              </div>
            </div>

          </motion.div>
        </div>
      </div>

      {/* Quick Timer Modal */}
      <QuickTimerModal
        isOpen={isQuickTimerModalOpen}
        onClose={() => setIsQuickTimerModalOpen(false)}
        onCreate={(duration, name) => {
          createRoom('quick', { name, duration }, name)
          setIsQuickTimerModalOpen(false)
        }}
        isCreating={isCreatingRoom}
      />

      {/* AI Timer Modal */}
      <AITimerModal
        isOpen={isAITimerModalOpen}
        onClose={() => setIsAITimerModalOpen(false)}
        onCreate={(segments, name) => {
          createRoom('ai', { segments, name })
          setIsAITimerModalOpen(false)
        }}
        isCreating={isCreatingRoom}
      />

      {/* Create Room Modal */}
      <CreateRoomModal
        isOpen={isCreateRoomModalOpen}
        onClose={() => setIsCreateRoomModalOpen(false)}
        onCreate={(name) => {
          createRoom('manual', undefined, name)
          setIsCreateRoomModalOpen(false)
        }}
        isCreating={isCreatingRoom}
      />

      {/* Delete Room Modal */}
      <DeleteRoomModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, room: null })}
        onConfirm={() => {
          if (deleteModal.room) {
            deleteRoom(deleteModal.room.id)
          }
        }}
        roomName={deleteModal.room?.name || ''}
        isDeleting={isDeletingRoom}
      />
    </DashboardLayout>
  )
}

// Helper function
function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}