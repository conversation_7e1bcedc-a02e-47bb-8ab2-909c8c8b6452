'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import DashboardLayout from '@/components/dashboard/dashboard-layout'
import { 
  Video, 
  Clock, 
  TrendingUp,
  ThumbsUp,
  MessageSquare,
  Bell,
  Sparkles,
  Play,
  Plus,
  Eye
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface YouTubeTemplate {
  id: string
  name: string
  description: string
  duration: string
  category: string
  gradient: string
  segments: Array<{
    name: string
    duration: number
    tips: string
  }>
}

const youtubeTemplates: YouTubeTemplate[] = [
  {
    id: 'tutorial',
    name: 'Tutorial Video',
    description: 'Step-by-step educational content',
    duration: '10 min',
    category: 'Education',
    gradient: 'from-red-500 to-pink-500',
    segments: [
      { name: 'Hook', duration: 15, tips: 'State the problem & show the result' },
      { name: 'Intro', duration: 30, tips: 'Welcome & explain what you\'ll teach' },
      { name: 'Overview', duration: 60, tips: 'Show the big picture' },
      { name: 'Step-by-Step', duration: 360, tips: 'Break down into clear steps' },
      { name: 'Recap', duration: 60, tips: 'Summarize key points' },
      { name: 'CTA & End Screen', duration: 30, tips: 'Subscribe reminder & next videos' }
    ]
  },
  {
    id: 'vlog',
    name: 'Vlog Style',
    description: 'Personal story or day-in-the-life',
    duration: '12 min',
    category: 'Lifestyle',
    gradient: 'from-purple-500 to-pink-500',
    segments: [
      { name: 'Teaser', duration: 20, tips: 'Preview exciting moments' },
      { name: 'Intro', duration: 40, tips: 'Set the scene & context' },
      { name: 'Morning/Start', duration: 180, tips: 'Begin the journey' },
      { name: 'Main Event', duration: 300, tips: 'Core content/activity' },
      { name: 'Reflection', duration: 120, tips: 'Share thoughts & lessons' },
      { name: 'Outro', duration: 40, tips: 'Thank viewers & tease next vlog' }
    ]
  },
  {
    id: 'review',
    name: 'Product Review',
    description: 'In-depth product analysis',
    duration: '15 min',
    category: 'Reviews',
    gradient: 'from-blue-500 to-cyan-500',
    segments: [
      { name: 'Hook', duration: 20, tips: 'Show the product in action' },
      { name: 'Introduction', duration: 60, tips: 'Product overview & specs' },
      { name: 'Unboxing', duration: 120, tips: 'First impressions' },
      { name: 'Features Demo', duration: 360, tips: 'Show all key features' },
      { name: 'Pros & Cons', duration: 180, tips: 'Balanced analysis' },
      { name: 'Final Verdict', duration: 90, tips: 'Recommendation & rating' },
      { name: 'Links & CTA', duration: 30, tips: 'Affiliate links & subscribe' }
    ]
  },
  {
    id: 'shorts',
    name: 'YouTube Shorts',
    description: 'Quick, engaging vertical content',
    duration: '60 sec',
    category: 'Shorts',
    gradient: 'from-green-500 to-emerald-500',
    segments: [
      { name: 'Hook', duration: 3, tips: 'Grab attention instantly' },
      { name: 'Setup', duration: 7, tips: 'Context or question' },
      { name: 'Main Content', duration: 40, tips: 'Deliver value fast' },
      { name: 'Punchline/Result', duration: 8, tips: 'Payoff or reveal' },
      { name: 'CTA', duration: 2, tips: 'Quick call to action' }
    ]
  },
  {
    id: 'gaming',
    name: 'Gaming Video',
    description: 'Let\'s play or gaming highlights',
    duration: '20 min',
    category: 'Gaming',
    gradient: 'from-indigo-500 to-purple-500',
    segments: [
      { name: 'Intro', duration: 30, tips: 'Game & episode context' },
      { name: 'Recap', duration: 60, tips: 'Previous episode summary' },
      { name: 'Gameplay Part 1', duration: 480, tips: 'Main gameplay with commentary' },
      { name: 'Mid-roll Break', duration: 30, tips: 'Stretch reminder or ad spot' },
      { name: 'Gameplay Part 2', duration: 480, tips: 'Continue gameplay' },
      { name: 'Outro', duration: 60, tips: 'Next episode preview' }
    ]
  },
  {
    id: 'toplist',
    name: 'Top 10 List',
    description: 'Countdown or ranking video',
    duration: '8 min',
    category: 'Entertainment',
    gradient: 'from-orange-500 to-red-500',
    segments: [
      { name: 'Intro', duration: 30, tips: 'Topic introduction' },
      { name: 'Honorable Mentions', duration: 30, tips: 'Quick extras' },
      { name: 'Numbers 10-6', duration: 150, tips: '30 sec each item' },
      { name: 'Numbers 5-2', duration: 160, tips: '40 sec each item' },
      { name: 'Number 1', duration: 60, tips: 'Most detail for #1' },
      { name: 'Wrap-up', duration: 30, tips: 'Summary & your picks' }
    ]
  }
]

export default function YouTubePage() {
  const [isCreating, setIsCreating] = useState<string | null>(null)
  const router = useRouter()

  const createYouTubeTimer = async (template: YouTubeTemplate) => {
    setIsCreating(template.id)
    try {
      const response = await fetch('/api/rooms/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'template',
          name: `YouTube: ${template.name}`,
          timerConfig: {
            segments: template.segments.map(seg => ({
              name: seg.name,
              duration: seg.duration
            }))
          }
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create room')
      }

      router.push(data.controllerUrl)
    } catch (error) {
      console.error('Error creating YouTube timer:', error)
      alert('Failed to create timer. Please try again.')
    } finally {
      setIsCreating(null)
    }
  }

  // Group templates by category
  const categories = ['Education', 'Lifestyle', 'Reviews', 'Shorts', 'Gaming', 'Entertainment']

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-red-500 to-pink-500">
              <Video className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white">
              YouTube Templates
            </h1>
          </div>
          <p className="text-gray-400 text-lg">
            Optimize your content creation with proven YouTube video structures
          </p>
        </motion.div>

        {/* Category Tabs */}
        <div className="flex gap-2 mb-8 overflow-x-auto pb-2">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => {
                const element = document.getElementById(category.toLowerCase())
                element?.scrollIntoView({ behavior: 'smooth' })
              }}
              className="px-4 py-2 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl text-sm font-medium whitespace-nowrap transition-all"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Templates Grid */}
        <div className="space-y-8">
          {categories.map(category => {
            const categoryTemplates = youtubeTemplates.filter(t => t.category === category)
            
            return (
              <div key={category} id={category.toLowerCase()}>
                <h2 className="text-xl font-bold text-white mb-4">{category}</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  {categoryTemplates.map((template, index) => (
                    <motion.div
                      key={template.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="group relative"
                    >
                      {/* Glow Effect */}
                      <div className={cn(
                        "absolute inset-0 rounded-2xl bg-gradient-to-r opacity-0 group-hover:opacity-10 blur-xl transition-opacity duration-500",
                        template.gradient
                      )} />

                      {/* Card */}
                      <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300">
                        {/* Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-white mb-1">{template.name}</h3>
                            <p className="text-gray-400 text-sm">{template.description}</p>
                          </div>
                          <span className="px-3 py-1 bg-gradient-to-r from-red-500/20 to-pink-500/20 rounded-full text-sm text-red-400">
                            {template.duration}
                          </span>
                        </div>

                        {/* Segments */}
                        <div className="space-y-2 mb-6 max-h-48 overflow-y-auto">
                          {template.segments.map((segment, i) => (
                            <div key={i} className="group/item">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm font-medium text-white">{segment.name}</span>
                                <span className="text-xs text-gray-500">
                                  {segment.duration >= 60 
                                    ? `${Math.floor(segment.duration / 60)}:${(segment.duration % 60).toString().padStart(2, '0')}`
                                    : `${segment.duration}s`
                                  }
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 group-hover/item:text-gray-400 transition">
                                💡 {segment.tips}
                              </p>
                            </div>
                          ))}
                        </div>

                        {/* Action Button */}
                        <button
                          onClick={() => createYouTubeTimer(template)}
                          disabled={isCreating === template.id}
                          className={cn(
                            "w-full py-3 rounded-xl font-medium flex items-center justify-center gap-2 transition-all",
                            "bg-gradient-to-r hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed text-white",
                            template.gradient
                          )}
                        >
                          {isCreating === template.id ? (
                            <span>Creating...</span>
                          ) : (
                            <>
                              <Play className="w-4 h-4" />
                              <span>Start Recording</span>
                            </>
                          )}
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>

        {/* YouTube Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-red-500/10 to-pink-500/10 rounded-2xl p-8 border border-red-500/20 mb-8"
        >
          <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-red-400" />
            YouTube Success Tips
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start gap-3">
              <Eye className="w-4 h-4 text-red-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">Hook in 3 Seconds</h4>
                <p className="text-sm text-gray-400">Viewers decide quickly - make your hook count</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <TrendingUp className="w-4 h-4 text-red-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">Consistent Pacing</h4>
                <p className="text-sm text-gray-400">Use timers to maintain energy throughout</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <ThumbsUp className="w-4 h-4 text-red-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">Clear CTAs</h4>
                <p className="text-sm text-gray-400">Time your subscribe reminders perfectly</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Bell className="w-4 h-4 text-red-400 mt-1" />
              <div>
                <h4 className="font-medium text-white mb-1">End Screen Timing</h4>
                <p className="text-sm text-gray-400">Leave 20 seconds for end screen elements</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Custom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center"
        >
          <button
            onClick={() => router.push('/dashboard')}
            className="inline-flex items-center gap-2 px-6 py-3 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl font-medium transition-all"
          >
            <Plus className="w-4 h-4" />
            <span>Create Custom Video Timer</span>
          </button>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}

// Helper function
function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}