import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET() {
  const supabase = await createClient()

  try {
    // Create a new room with default settings
    const { data: room, error } = await supabase
      .from('rooms')
      .insert({
        name: 'Untitled Room',
        is_public: true,
        settings: {},
        theme: {
          primaryColor: '#00DC82',
          secondaryColor: '#FF0080'
        }
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating room:', error)
      return NextResponse.json({ error: 'Failed to create room' }, { status: 500 })
    }

    // Create default timers for the room
    const defaultTimers = [
      { room_id: room.id, name: 'Timer 1', duration: 300, position: 0 },
      { room_id: room.id, name: 'Timer 2', duration: 600, position: 1 },
      { room_id: room.id, name: 'Timer 3', duration: 600, position: 2 }
    ]

    const { error: timersError } = await supabase
      .from('timers')
      .insert(defaultTimers)

    if (timersError) {
      console.error('Error creating timers:', timersError)
      // Still redirect even if timers fail
    }

    // Redirect to the room controller
    return NextResponse.redirect(new URL(`/r/${room.slug}/controller`, process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'))
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 })
  }
}