'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Room, Timer } from '@/types/database'
import TimerList from '@/components/timer/timer-list'
import TimerControls from '@/components/timer/timer-controls'
import SessionControls from '@/components/timer/session-controls'
import AddTimerModal from '@/components/timer/add-timer-modal'
import FlashMessageModal from '@/components/timer/flash-message-modal'
import { Clock, Settings, Share2, ExternalLink, Check, RotateCcw, ArrowLeft, Zap, Crown, AlertTriangle } from 'lucide-react'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { useSubscription } from '@/hooks/useSubscription'
import { isTimerLimitReached, getRemainingTime, formatTimeLimit, TIMER_LIMITS } from '@/lib/timer-limits'

export default function ControllerPage() {
  const params = useParams()
  const slug = params.slug as string
  const supabase = createClient()

  const [room, setRoom] = useState<Room | null>(null)
  const [timers, setTimers] = useState<Timer[]>([])
  const [currentTimerId, setCurrentTimerId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [isAddTimerModalOpen, setIsAddTimerModalOpen] = useState(false)
  const [showCopiedToast, setShowCopiedToast] = useState(false)
  const [isResettingSession, setIsResettingSession] = useState(false)
  const [isFlashMessageModalOpen, setIsFlashMessageModalOpen] = useState(false)
  const [isSendingFlashMessage, setIsSendingFlashMessage] = useState(false)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [timerReachedLimit, setTimerReachedLimit] = useState(false)

  const { plan, createCheckout } = useSubscription()

  // Load room data
  useEffect(() => {
    async function loadRoom() {
      const { data: roomData, error } = await supabase
        .from('rooms')
        .select('*')
        .eq('slug', slug)
        .single()

      if (error || !roomData) {
        console.error('Error loading room:', error)
        return
      }

      setRoom(roomData)

      // Load timers
      const { data: timersData } = await supabase
        .from('timers')
        .select('*')
        .eq('room_id', roomData.id)
        .order('position')

      if (timersData) {
        setTimers(timersData)
        if (timersData.length > 0 && !currentTimerId) {
          // First try to find a running timer
          const runningTimer = timersData.find(t => t.status === 'running')
          if (runningTimer) {
            setCurrentTimerId(runningTimer.id)
            console.log('Auto-selected running timer:', runningTimer.name)
          } else {
            // Otherwise select the first timer
            setCurrentTimerId(timersData[0].id)
          }
        }
      }



      setLoading(false)
    }

    loadRoom()
  }, [slug, supabase, currentTimerId])

  // Function to refresh timers manually
  const refreshTimers = async () => {
    if (!room) return

    try {
      const { data: timersData } = await supabase
        .from('timers')
        .select('*')
        .eq('room_id', room.id)
        .order('position')

      if (timersData) {
        setTimers(timersData as Timer[])
        console.log('Refreshed timers:', timersData.length)
      }
    } catch (error) {
      console.error('Error refreshing timers:', error)
    }
  }

  // Subscribe to real-time updates
  useEffect(() => {
    if (!room) return

    console.log('Setting up subscription for room:', room.id)

    // Subscribe to timer updates with simpler configuration
    const timerSubscription = supabase
      .channel(`room-${room.id}-timers-${Date.now()}`) // Add timestamp to ensure unique channel
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'timers',
          filter: `room_id=eq.${room.id}`
        },
        async (payload) => {
          console.log('Real-time timer update:', payload)

          // Always refresh timers to ensure consistency
          await refreshTimers()

          if (payload.eventType === 'UPDATE') {
            // Auto-switch to running timer
            if (payload.new?.status === 'running') {
              console.log('Switching to running timer:', payload.new.name)
              setCurrentTimerId(payload.new.id)
            }

            // Check timer limits for free users
            if (payload.new?.status === 'running' && plan?.plan_type === 'free') {
              const elapsedTime = payload.new.elapsed_time || 0
              if (isTimerLimitReached('free', elapsedTime)) {
                console.log('Timer limit reached for free user, stopping timer')
                setTimerReachedLimit(true)
                setShowUpgradeModal(true)
                
                // Stop the timer
                await supabase
                  .from('timers')
                  .update({ status: 'paused' })
                  .eq('id', payload.new.id)
              }
            }
          }
        }
      )
      .subscribe((status, err) => {
        console.log('Subscription status:', status, err ? 'Error:' : '', err)
        if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to timer updates')
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Channel error:', err)
          // Try to refresh timers manually if subscription fails
          refreshTimers()
        } else if (status === 'TIMED_OUT') {
          console.error('❌ Subscription timed out')
          refreshTimers()
        } else if (status === 'CLOSED') {
          console.log('🔌 Subscription closed')
        }
      })

    // Set up a fallback polling mechanism
    const pollInterval = setInterval(refreshTimers, 2000) // Poll every 2 seconds as fallback

    return () => {
      console.log('Cleaning up subscription and polling')
      timerSubscription.unsubscribe()
      clearInterval(pollInterval)
    }
  }, [room, supabase])

  // Check if session is complete
  const isSessionComplete = timers.length > 0 && timers.every(timer => timer.status === 'finished')

  // Reset entire session
  const resetSession = async () => {
    if (!room || isResettingSession) return

    setIsResettingSession(true)
    try {
      // Reset all timers to idle state
      const { error } = await supabase
        .from('timers')
        .update({
          status: 'idle',
          elapsed_time: 0,
          started_at: null
        })
        .eq('room_id', room.id)

      if (error) {
        console.error('Error resetting session:', error)
      } else {
        console.log('Session reset successfully')
        // Clear current timer selection
        setCurrentTimerId(null)
      }
    } catch (error) {
      console.error('Error resetting session:', error)
    }
    setIsResettingSession(false)
  }

  // Send flash message
  const sendFlashMessage = async (message: string, duration: number) => {
    if (!room) return
    
    setIsSendingFlashMessage(true)
    try {
      // Insert a flash message into the messages table
      const { error } = await supabase
        .from('messages')
        .insert({
          room_id: room.id,
          text: message,
          style: {
            color: 'white',
            bold: true,
            uppercase: false,
            flash: true,
            duration: duration
          },
          visible: true,
          source: 'controller'
        })

      if (error) {
        console.error('Error sending flash message:', error)
        alert('Failed to send flash message')
      } else {
        console.log('Flash message sent successfully')
        setIsFlashMessageModalOpen(false)
      }
    } catch (error) {
      console.error('Error sending flash message:', error)
    }
    setIsSendingFlashMessage(false)
  }

  // Handle upgrade to pro
  const handleUpgrade = async () => {
    try {
      // Using the actual Polar.sh product ID from your account
      const proProductId = '8c1c7045-9830-42d2-928c-939ff50042df'
      const checkoutUrl = await createCheckout(proProductId)
      
      if (checkoutUrl) {
        window.open(checkoutUrl, '_blank')
      }
    } catch (error) {
      console.error('Error creating checkout:', error)
      alert('Failed to start upgrade process. Please try again.')
    }
  }

  if (loading || !room) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  const currentTimer = timers.find(t => t.id === currentTimerId) || null

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-950 to-black text-white">
      {/* Header */}
      <header className="border-b border-gray-800/50 bg-gray-900/30 backdrop-blur-xl">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <button className="flex items-center space-x-2 px-4 py-2 hover:bg-gray-800/50 rounded-xl transition-all hover:scale-105 text-gray-300 hover:text-white" title="Back to Dashboard">
                  <ArrowLeft className="w-5 h-5" />
                  <span className="hidden sm:inline">Dashboard</span>
                </button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl blur opacity-50" />
                  <div className="relative w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-black" />
                  </div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    {room.name}
                  </h1>
                  {/* TODO: Uncomment when description field is added to database
                  {room.description && (
                    <p className="text-sm text-gray-400 mt-1">{room.description}</p>
                  )} */}
                  <p className="text-sm text-gray-400 flex items-center space-x-2 mt-1">
                    <span>Controller</span>
                    <span className="w-1 h-1 bg-gray-600 rounded-full" />
                    <span className="text-green-400">Live</span>
                  </p>
                </div>
              </div>

            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setIsFlashMessageModalOpen(true)}
                className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 rounded-xl transition-all hover:scale-105 shadow-lg"
                title="Send urgent message to viewer"
              >
                <Zap className="w-4 h-4" />
                <span className="font-medium">Flash Message</span>
              </button>
              <Link href={`/r/${room.slug}`} target="_blank" rel="noopener noreferrer">
                <button 
                  className="flex items-center space-x-2 px-4 py-3 bg-gray-800/50 hover:bg-gray-700/50 rounded-xl transition-all hover:scale-105"
                  title="Open viewer in new tab"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span className="font-medium">Open Viewer</span>
                </button>
              </Link>
              <button
                onClick={() => {
                  const url = `${window.location.origin}/r/${room.slug}`
                  navigator.clipboard.writeText(url)
                  setShowCopiedToast(true)
                  setTimeout(() => setShowCopiedToast(false), 3000)
                }}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-xl transition-all hover:scale-105 shadow-lg"
              >
                <Share2 className="w-4 h-4" />
                <span className="font-medium">Share Link</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar */}
        <aside className="w-[480px] border-r border-gray-800/50 bg-gray-900/20 backdrop-blur-sm overflow-y-auto">
          <div className="p-8">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold flex items-center space-x-3">
                <Clock className="w-6 h-6 text-green-500" />
                <span>Timers</span>
              </h2>
              <button
                onClick={() => setIsAddTimerModalOpen(true)}
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-black text-sm font-bold rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all hover:scale-105 shadow-lg"
              >
                Add Timer
              </button>
            </div>
            <TimerList
              timers={timers}
              currentTimerId={currentTimerId}
              onSelectTimer={setCurrentTimerId}
              roomId={room.id}
            />
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 flex flex-col">
          {/* Session Progress */}
          {timers.length > 1 && (
            <div className="bg-gray-900/30 backdrop-blur-sm border-b border-gray-800/50 p-6">
              <div className="container mx-auto px-8">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-white">Session Progress</h3>
                  <div className="text-sm text-gray-400">
                    {timers.findIndex(t => t.id === currentTimerId) + 1} of {timers.length} segments
                  </div>
                </div>
                
                {/* Overall Progress Bar */}
                <div className="mb-3">
                  <div className="h-2 bg-gray-800/50 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-green-500 to-emerald-500 transition-all duration-500"
                      style={{
                        width: `${((timers.findIndex(t => t.id === currentTimerId) + 1) / timers.length) * 100}%`
                      }}
                    />
                  </div>
                </div>

                {/* Timer Segments */}
                <div className="flex space-x-2 overflow-x-auto">
                  {timers.map((t, index) => {
                    const isCurrent = t.id === currentTimerId
                    const isPast = index < timers.findIndex(timer => timer.id === currentTimerId)
                    const totalMinutes = Math.ceil(t.duration / 60)
                    
                    return (
                      <div
                        key={t.id}
                        className={cn(
                          "flex-shrink-0 px-3 py-2 rounded-lg text-xs font-medium border transition-all",
                          isCurrent ? "bg-green-500/20 border-green-500/50 text-green-400" :
                          isPast ? "bg-gray-600/20 border-gray-600/50 text-gray-500" :
                          "bg-gray-800/50 border-gray-700/50 text-gray-400"
                        )}
                      >
                        <div className="text-center">
                          <div className="font-semibold">{t.name}</div>
                          <div className="opacity-75">{totalMinutes}min</div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Current Segment Info */}
                {currentTimer && (
                  <div className="mt-3 text-center">
                    <div className="text-sm text-gray-400">
                      Currently: <span className="text-white font-medium">{currentTimer.name}</span>
                      {currentTimer.status === 'running' && (
                        <span className="ml-2 text-green-400">● Live</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Session Complete Banner */}
          {isSessionComplete && (
            <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-b border-green-500/30 p-6">
              <div className="container mx-auto px-8">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                      <Check className="w-6 h-6 text-black" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-green-400">Session Complete!</h3>
                      <p className="text-gray-300">All {timers.length} segments have been completed successfully.</p>
                    </div>
                  </div>
                  <button
                    onClick={resetSession}
                    disabled={isResettingSession}
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-medium rounded-xl transition-all hover:scale-105 shadow-lg disabled:cursor-not-allowed"
                  >
                    <RotateCcw className="w-4 h-4" />
                    <span>{isResettingSession ? 'Resetting...' : 'Reset Session'}</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Free User Timer Limit Warning */}
          {plan?.plan_type === 'free' && currentTimer && currentTimer.status === 'running' && (
            <div className="bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-b border-amber-500/30 p-4">
              <div className="container mx-auto px-8">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="w-5 h-5 text-amber-400" />
                    <div>
                      <p className="text-amber-400 font-medium">Free Plan Limit</p>
                      <p className="text-sm text-gray-300">
                        Timer will automatically stop at {formatTimeLimit(TIMER_LIMITS.FREE)} limit
                        {currentTimer.elapsed_time && (
                          <>
                            {' • '}
                            <span className={cn(
                              getRemainingTime('free', currentTimer.elapsed_time) <= 60 ? 'text-red-400' : 'text-amber-400'
                            )}>
                              {formatTimeLimit(getRemainingTime('free', currentTimer.elapsed_time))} remaining
                            </span>
                          </>
                        )}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowUpgradeModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-500 text-black font-medium rounded-lg hover:from-amber-600 hover:to-orange-600 transition-all hover:scale-105"
                  >
                    <Crown className="w-4 h-4" />
                    <span>Upgrade to Pro</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Timer Display & Controls */}
          <div className="flex-1 flex items-center justify-center p-12">
            <div className="space-y-8">
              {/* Timer Display */}
              {currentTimer ? (
                <TimerControls
                  timer={currentTimer}
                  roomId={room.id}
                  allTimers={timers}
                  userPlan={plan}
                  onNext={() => {
                    // Handle automatic progression to next timer
                    const currentIndex = timers.findIndex(t => t.id === currentTimerId)
                    if (currentIndex < timers.length - 1) {
                      const nextTimer = timers[currentIndex + 1]
                      setCurrentTimerId(nextTimer.id)
                      console.log('Auto-switched to next timer:', nextTimer.name)
                    }
                  }}
                  onUpgradeNeeded={() => setShowUpgradeModal(true)}
                />
              ) : (
                <div className="text-center text-gray-500">
                  <Clock className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p>No timer selected</p>
                  <p className="text-sm mt-2">Use the session controls below to start</p>
                </div>
              )}

              {/* Session Controls */}
              <SessionControls
                timers={timers}
                currentTimer={currentTimer}
                onTimerChange={setCurrentTimerId}
                roomId={room.id}
              />
            </div>
          </div>


        </main>
      </div>

      {/* Add Timer Modal */}
      <AddTimerModal
        isOpen={isAddTimerModalOpen}
        onClose={() => setIsAddTimerModalOpen(false)}
        roomId={room.id}
        existingTimers={timers}
      />

      {/* Flash Message Modal */}
      <FlashMessageModal
        isOpen={isFlashMessageModalOpen}
        onClose={() => setIsFlashMessageModalOpen(false)}
        onSend={sendFlashMessage}
        isSending={isSendingFlashMessage}
      />

      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-2xl border border-gray-700 max-w-md w-full p-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                <Crown className="w-8 h-8 text-black" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                {timerReachedLimit ? 'Timer Limit Reached!' : 'Upgrade to Pro'}
              </h3>
              <p className="text-gray-300 mb-6">
                {timerReachedLimit 
                  ? `Your timer has reached the ${formatTimeLimit(TIMER_LIMITS.FREE)} free plan limit and has been paused.`
                  : 'Get unlimited timer duration and access to premium features.'
                }
              </p>
              
              <div className="space-y-4">
                <div className="bg-gray-800 rounded-lg p-4">
                  <h4 className="font-semibold text-white mb-2">Pro Plan Benefits:</h4>
                  <ul className="text-sm text-gray-300 space-y-1">
                    <li>• Unlimited timer duration</li>
                    <li>• AI-powered speech parsing</li>
                    <li>• Advanced collaboration features</li>
                    <li>• Priority support</li>
                  </ul>
                </div>
                
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setShowUpgradeModal(false)
                      setTimerReachedLimit(false)
                    }}
                    className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  >
                    {timerReachedLimit ? 'Continue with Free' : 'Maybe Later'}
                  </button>
                  <button
                    onClick={handleUpgrade}
                    className="flex-1 px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-500 text-black font-medium rounded-lg hover:from-amber-600 hover:to-orange-600 transition-all"
                  >
                    Upgrade Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Toast Notification */}
      {showCopiedToast && (
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50">
          <div className="bg-gray-800 text-white px-6 py-3 rounded-xl shadow-lg flex items-center space-x-2">
            <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Viewer link copied to clipboard!</span>
          </div>
        </div>
      )}

    </div>
  )
}