'use client'

import { useEffect, useState, useCallback } from 'react'
import { useParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Room, Timer, Message } from '@/types/database'
import { cn } from '@/lib/utils'
import { Check } from 'lucide-react'
import { RotateCw, FlipHorizontal, FlipVertical } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

export default function ViewerPage() {
  const params = useParams()
  const slug = params.slug as string
  const supabase = createClient()

  const [room, setRoom] = useState<Room | null>(null)
  const [currentTimer, setCurrentTimer] = useState<Timer | null>(null)
  const [allTimers, setAllTimers] = useState<Timer[]>([])
  const [displayTime, setDisplayTime] = useState(0)
  const [loading, setLoading] = useState(true)
  const [flipHorizontal, setFlipHorizontal] = useState(false)
  const [flipVertical, setFlipVertical] = useState(false)
  const [rotated, setRotated] = useState(false)
  const [flashMessage, setFlashMessage] = useState<{ text: string; duration: number } | null>(null)

  // Calculate display time
  const calculateDisplayTime = useCallback(() => {
    if (!currentTimer) return 0

    if (currentTimer.type === 'tod') {
      return 0 // Time of day handled differently
    }

    if (currentTimer.status === 'idle') {
      return currentTimer.duration
    }

    if (currentTimer.status === 'finished') {
      return currentTimer.type === 'countdown' ? 0 : currentTimer.elapsed_time
    }

    if (currentTimer.status === 'running' && currentTimer.started_at) {
      const startTime = new Date(currentTimer.started_at).getTime()
      const now = Date.now()
      const elapsed = Math.floor((now - startTime) / 1000) + currentTimer.elapsed_time

      if (currentTimer.type === 'countdown') {
        const remaining = currentTimer.duration - elapsed
        return remaining > 0 || currentTimer.overtime_allowed ? remaining : 0
      } else {
        return elapsed
      }
    }

    return currentTimer.type === 'countdown' 
      ? currentTimer.duration - currentTimer.elapsed_time 
      : currentTimer.elapsed_time
  }, [currentTimer])

  // Format time display
  const formatTime = (seconds: number) => {
    if (currentTimer?.type === 'tod') {
      const now = new Date()
      return now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const absSeconds = Math.abs(seconds)
    const hours = Math.floor(absSeconds / 3600)
    const mins = Math.floor((absSeconds % 3600) / 60)
    const secs = absSeconds % 60

    let timeStr = ''
    if (hours > 0) {
      timeStr = `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    } else {
      timeStr = `${mins}:${secs.toString().padStart(2, '0')}`
    }

    return seconds < 0 ? `-${timeStr}` : timeStr
  }

  // Get current segment info
  const getCurrentSegmentInfo = () => {
    if (!currentTimer || !allTimers.length) return null

    const currentIndex = allTimers.findIndex(t => t.id === currentTimer.id)
    const nextTimer = currentIndex < allTimers.length - 1 ? allTimers[currentIndex + 1] : null

    return {
      current: currentIndex + 1,
      total: allTimers.length,
      nextTimer
    }
  }

  // Generate timeline colors for segments
  const getSegmentColor = (index: number, total: number) => {
    const colors = [
      'bg-green-500',
      'bg-blue-500',
      'bg-purple-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-orange-500'
    ]
    return colors[index % colors.length]
  }

  // Check if all timers are finished
  const isSessionComplete = allTimers.length > 0 && allTimers.every(timer => timer.status === 'finished')

  // Load room data
  useEffect(() => {
    async function loadRoom() {
      const { data: roomData, error } = await supabase
        .from('rooms')
        .select('*')
        .eq('slug', slug)
        .single()

      if (error || !roomData) {
        console.error('Error loading room:', error)
        return
      }

      setRoom(roomData)



      setLoading(false)
    }

    loadRoom()
  }, [slug, supabase])

  // Subscribe to real-time updates
  useEffect(() => {
    if (!room) return

    // Get all timers and current timer
    const loadTimers = async () => {
      const { data: timers } = await supabase
        .from('timers')
        .select('*')
        .eq('room_id', room.id)
        .order('position')

      if (timers) {
        setAllTimers(timers)
        
        // Find running timer first
        const runningTimer = timers.find(t => t.status === 'running')
        if (runningTimer) {
          setCurrentTimer(runningTimer)
        } else {
          // Find paused timer
          const pausedTimer = timers.find(t => t.status === 'paused')
          if (pausedTimer) {
            setCurrentTimer(pausedTimer)
          } else if (timers.length > 0) {
            // Default to first timer
            setCurrentTimer(timers[0])
          }
        }
      }
    }

    loadTimers()

    // Subscribe to timer updates
    const timerSubscription = supabase
      .channel(`viewer-${room.id}-timers`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'timers',
          filter: `room_id=eq.${room.id}`
        },
        (payload) => {
          console.log('Viewer: Real-time timer update:', payload)
          const updatedTimer = payload.new as Timer
          
          if (payload.eventType === 'UPDATE') {
            // Update allTimers
            setAllTimers(prev => prev.map(t => t.id === updatedTimer.id ? updatedTimer : t))
            
            if (currentTimer && updatedTimer.id === currentTimer.id) {
              setCurrentTimer(updatedTimer)
            }
            // Switch to newly started timer
            if (updatedTimer.status === 'running' && (!currentTimer || currentTimer.id !== updatedTimer.id)) {
              console.log('Viewer: Switching to running timer:', updatedTimer.name)
              setCurrentTimer(updatedTimer)
            }
          } else if (payload.eventType === 'INSERT') {
            // Add new timer to allTimers
            setAllTimers(prev => [...prev, updatedTimer].sort((a, b) => a.position - b.position))
            
            // If new timer is running, switch to it
            if (updatedTimer.status === 'running') {
              console.log('Viewer: New running timer added:', updatedTimer.name)
              setCurrentTimer(updatedTimer)
            }
          } else if (payload.eventType === 'DELETE') {
            setAllTimers(prev => prev.filter(t => t.id !== payload.old.id))
          }
        }
      )
      .subscribe((status, err) => {
        console.log('Viewer subscription status:', status, err ? 'Error:' : '', err)
        if (status === 'SUBSCRIBED') {
          console.log('✅ Viewer successfully subscribed to timer updates')
        }
      })

    // Set up a fallback polling mechanism for viewer
    const pollInterval = setInterval(loadTimers, 3000) // Poll every 3 seconds

    return () => {
      timerSubscription.unsubscribe()
      clearInterval(pollInterval)
    }
  }, [room, supabase])

  // Subscribe to messages for flash messages
  useEffect(() => {
    if (!room) return

    console.log('Setting up message subscription for room:', room.id)
    
    // Check for recent flash messages on load
    const checkRecentMessages = async () => {
      const fiveSecondsAgo = new Date(Date.now() - 5000).toISOString()
      const { data: recentMessages } = await supabase
        .from('messages')
        .select('*')
        .eq('room_id', room.id)
        .eq('visible', true)
        .gte('created_at', fiveSecondsAgo)
        .order('created_at', { ascending: false })
        .limit(1)
      
      if (recentMessages && recentMessages.length > 0) {
        const message = recentMessages[0]
        if (message.style && typeof message.style === 'object' && 'flash' in message.style && message.style.flash) {
          const duration = (message.style as any).duration || 5
          const messageAge = Date.now() - new Date(message.created_at).getTime()
          const remainingDuration = Math.max(0, (duration * 1000 - messageAge) / 1000)
          
          if (remainingDuration > 0) {
            console.log('Found recent flash message:', message.text, 'remaining duration:', remainingDuration)
            setFlashMessage({ text: message.text, duration: remainingDuration })
            
            setTimeout(() => {
              setFlashMessage(null)
            }, remainingDuration * 1000)
          }
        }
      }
    }
    
    // Check on mount
    checkRecentMessages()
    
    const messageSubscription = supabase
      .channel(`room-${room.id}-messages`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `room_id=eq.${room.id}`
        },
        (payload) => {
          console.log('Received message:', payload)
          const message = payload.new as Message
          if (message.style && typeof message.style === 'object' && 'flash' in message.style && message.style.flash) {
            const duration = (message.style as any).duration || 5
            console.log('Flash message detected:', message.text, 'duration:', duration)
            setFlashMessage({ text: message.text, duration })
            
            // Auto-hide after duration
            setTimeout(() => {
              setFlashMessage(null)
            }, duration * 1000)
          }
        }
      )
      .subscribe((status, err) => {
        console.log('Message subscription status:', status, err ? 'Error:' : '', err)
      })
    
    // Fallback polling mechanism
    const pollInterval = setInterval(checkRecentMessages, 2000)

    return () => {
      messageSubscription.unsubscribe()
      clearInterval(pollInterval)
    }
  }, [room, supabase])

  // Update display time
  useEffect(() => {
    const updateDisplay = () => {
      setDisplayTime(calculateDisplayTime())
    }

    updateDisplay()
    
    if (currentTimer?.status === 'running') {
      const interval = setInterval(updateDisplay, 100)
      return () => clearInterval(interval)
    }
  }, [currentTimer, calculateDisplayTime])

  if (loading || !room) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-2xl">Loading...</div>
      </div>
    )
  }

  const isWrapUp = currentTimer?.type === 'countdown' && 
    displayTime > 0 && 
    displayTime <= (currentTimer?.wrap_up_time || 60)

  const isOvertime = currentTimer?.type === 'countdown' && displayTime < 0

  // Get current timer index and next timer
  const currentIndex = currentTimer ? allTimers.findIndex(t => t.id === currentTimer.id) : -1
  const nextTimer = currentIndex >= 0 && currentIndex < allTimers.length - 1 ? allTimers[currentIndex + 1] : null

  // Format duration for display
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    if (mins >= 60) {
      const hours = Math.floor(mins / 60)
      const remainingMins = mins % 60
      return `${hours}:${remainingMins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }





  // Calculate transform style
  const transformStyle = {
    transform: `${flipHorizontal ? 'scaleX(-1)' : ''} ${flipVertical ? 'scaleY(-1)' : ''} ${rotated ? 'rotate(180deg)' : ''}`.trim()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-950 to-black text-white flex flex-col items-center justify-center p-8 relative overflow-hidden" style={transformStyle}>
      {/* View Control Buttons */}
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
        <button
          onClick={() => setFlipHorizontal(!flipHorizontal)}
          className={cn(
            "p-3 rounded-lg transition-all duration-200 border border-white/20 backdrop-blur-sm",
            flipHorizontal ? "bg-green-500/20 border-green-500/50" : "bg-black/30 hover:bg-white/10"
          )}
          title="Flip Horizontally"
        >
          <FlipHorizontal className="w-5 h-5" />
        </button>
        <button
          onClick={() => setFlipVertical(!flipVertical)}
          className={cn(
            "p-3 rounded-lg transition-all duration-200 border border-white/20 backdrop-blur-sm",
            flipVertical ? "bg-green-500/20 border-green-500/50" : "bg-black/30 hover:bg-white/10"
          )}
          title="Flip Vertically"
        >
          <FlipVertical className="w-5 h-5" />
        </button>
        <button
          onClick={() => setRotated(!rotated)}
          className={cn(
            "p-3 rounded-lg transition-all duration-200 border border-white/20 backdrop-blur-sm",
            rotated ? "bg-green-500/20 border-green-500/50" : "bg-black/30 hover:bg-white/10"
          )}
          title="Rotate 180°"
        >
          <RotateCw className="w-5 h-5" />
        </button>
      </div>

      {/* Background Effects */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }} />
      </div>

      {/* Session Complete Message */}
      {isSessionComplete && (
        <div className="text-center relative z-10">
          <div className="mb-8">
            <div className="w-32 h-32 mx-auto mb-8 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center animate-pulse">
              <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">
                <Check className="w-8 h-8 text-green-500" />
              </div>
            </div>
          </div>

          <h1 className="text-5xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-green-400 via-emerald-400 to-green-500 bg-clip-text text-transparent">
            Session Complete!
          </h1>

          <div className="text-2xl lg:text-3xl text-gray-300 mb-8">
            Thank you for watching
          </div>

          <div className="text-lg text-gray-400 max-w-2xl mx-auto">
            All {allTimers.length} segments have been completed successfully.
          </div>
        </div>
      )}

      {/* Timer Display */}
      {currentTimer && !isSessionComplete && (
        <div className="text-center relative z-10">
          {/* Segment Information */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-6 mb-4">
              <div className="text-2xl lg:text-3xl font-bold text-blue-400">
                Segment {currentIndex + 1} of {allTimers.length}
              </div>
              <div className={cn(
                "flex items-center space-x-2 px-4 py-2 rounded-full border",
                currentTimer.status === 'running' ? "border-green-500/50 bg-green-500/10" :
                currentTimer.status === 'paused' ? "border-yellow-500/50 bg-yellow-500/10" :
                "border-gray-700 bg-gray-800/50"
              )}>
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  currentTimer.status === 'running' ? "bg-green-500 animate-pulse" :
                  currentTimer.status === 'paused' ? "bg-yellow-500" :
                  "bg-gray-500"
                )} />
                <span className="text-sm font-medium capitalize">{currentTimer.status}</span>
              </div>
            </div>

            <h1 className="text-6xl lg:text-8xl font-bold mb-6 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              {currentTimer.name}
            </h1>

            {/* Timer Description */}
            {currentTimer.description && (
              <p className="text-2xl lg:text-3xl text-gray-300 mb-6 max-w-4xl mx-auto">
                {currentTimer.description}
              </p>
            )}

            {/* Upcoming Timer */}
            {nextTimer && (
              <div className="text-xl lg:text-2xl text-gray-400 mb-4">
                <span className="text-gray-500">Upcoming:</span>{' '}
                <span className="text-white font-semibold">{nextTimer.name}</span>{' '}
                <span className="text-gray-500">({formatDuration(nextTimer.duration)})</span>
              </div>
            )}
          </div>

          <div className="relative">
            {/* Glow effect behind timer */}
            <div className={cn(
              "absolute inset-0 blur-3xl opacity-50 transition-colors duration-500",
              isOvertime ? "bg-red-500/30" : isWrapUp ? "bg-yellow-500/30" : "bg-white/20"
            )} />

            <div
              className={cn(
                "relative font-mono text-[8rem] lg:text-[12rem] xl:text-[16rem] font-bold tracking-tight transition-all duration-500 leading-none",
                "drop-shadow-2xl",
                isOvertime ? "text-red-400 animate-pulse" :
                isWrapUp ? "text-yellow-400" :
                "text-white"
              )}
            >
              {formatTime(displayTime)}
            </div>
          </div>
        </div>
      )}



      {/* No timer message */}
      {!currentTimer && !isSessionComplete && (
        <div className="text-center relative z-10">
          <div className="mb-8">
            <div className="w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-gray-800 to-gray-700 flex items-center justify-center">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-gray-600 to-gray-500 flex items-center justify-center">
                <div className="w-8 h-8 rounded-full bg-gray-400 animate-pulse" />
              </div>
            </div>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-300 to-gray-500 bg-clip-text text-transparent">
            Waiting for Timer
          </h2>
          <p className="text-xl text-gray-400 max-w-md mx-auto leading-relaxed">
            The presenter will start the timer soon. This screen will update automatically.
          </p>
        </div>
      )}

      {/* Visual Timeline */}
      {allTimers.length > 1 && (
        <div className="fixed bottom-0 left-0 right-0 z-20 bg-black/80 backdrop-blur-sm border-t border-white/10">
          <div className="px-8 py-4">
            <div className="flex items-center justify-center space-x-1 max-w-6xl mx-auto">
              {allTimers.map((timer, index) => {
                const totalDuration = allTimers.reduce((sum, t) => sum + t.duration, 0)
                const widthPercentage = (timer.duration / totalDuration) * 100
                const isActive = currentTimer?.id === timer.id

                return (
                  <div
                    key={timer.id}
                    className={cn(
                      "h-8 transition-all duration-300 flex items-center justify-center text-xs font-medium text-white relative overflow-hidden",
                      isActive ? "ring-2 ring-white ring-offset-2 ring-offset-black" : "",
                      getSegmentColor(index, allTimers.length)
                    )}
                    style={{ width: `${Math.max(widthPercentage, 8)}%` }}
                    title={`${timer.name} (${formatDuration(timer.duration)})`}
                  >
                    {/* Segment number */}
                    <span className="relative z-10 drop-shadow-sm">
                      {index + 1}
                    </span>

                    {/* Active indicator */}
                    {isActive && (
                      <div className="absolute inset-0 bg-white/20 animate-pulse" />
                    )}
                  </div>
                )
              })}
            </div>

            {/* Timeline labels */}
            <div className="flex items-center justify-between mt-2 text-xs text-gray-400 max-w-6xl mx-auto">
              <span>Start</span>
              <span>Total: {formatDuration(allTimers.reduce((sum, t) => sum + t.duration, 0))}</span>
            </div>
          </div>
        </div>
      )}

      {/* Flash Message Banner */}
      <AnimatePresence>
        {flashMessage && (
          <motion.div
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -100, opacity: 0 }}
            transition={{ type: "spring", damping: 20 }}
            className="fixed top-0 left-0 right-0 z-50 pointer-events-none"
          >
            <div className="bg-gradient-to-br from-black via-gray-950 to-black p-6 lg:p-8 relative overflow-hidden border-b border-gray-800">
              {/* Blinking white border */}
              <motion.div
                animate={{
                  opacity: [1, 0, 1]
                }}
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute inset-0 border-4 border-white"
              />
              
              {/* Flash effect background */}
              <motion.div
                animate={{
                  opacity: [0.3, 0.6, 0.3]
                }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute inset-0 bg-green-500/20"
              />
              
              {/* Message content */}
              <div className="relative text-center">
                <p className="text-3xl lg:text-5xl xl:text-6xl font-bold text-white">
                  {flashMessage.text}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}