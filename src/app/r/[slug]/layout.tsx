import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'

export async function generateMetadata({
  params
}: {
  params: { slug: string }
}): Promise<Metadata> {
  const supabase = await createClient()
  
  const { data: room } = await supabase
    .from('rooms')
    .select('name')
    .eq('slug', params.slug)
    .single()

  return {
    title: room ? `${room.name} | TimedFlow` : 'Room Not Found | TimedFlow',
  }
}

export default async function RoomLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: { slug: string }
}) {
  const supabase = await createClient()
  
  const { data: room, error } = await supabase
    .from('rooms')
    .select('*')
    .eq('slug', params.slug)
    .single()

  if (error || !room) {
    notFound()
  }

  return <>{children}</>
}