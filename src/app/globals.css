@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 4%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 100%;
    --primary: 162 100% 43%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 4%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 8%;
    --muted-foreground: 0 0% 63%;
    --accent: 338 100% 50%;
    --accent-foreground: 0 0% 100%;
    --destructive: 4 91% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 8%;
    --input: 0 0% 8%;
    --ring: 162 100% 43%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .glass {
    @apply backdrop-blur-xl bg-white/5 border border-white/10;
  }
  
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent;
  }
  
  .glow {
    @apply relative;
  }
  
  .glow::before {
    @apply absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-lg blur-lg opacity-30 group-hover:opacity-50 transition duration-300;
    content: "";
  }
  
  .glow-text {
    text-shadow: 0 0 20px rgba(0, 220, 130, 0.5);
  }
} 