export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          email: string
          full_name: string | null
          avatar_url: string | null
          username: string | null
        }
        Insert: {
          id: string
          created_at?: string
          updated_at?: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          username?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          username?: string | null
        }
      }
      rooms: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          description?: string | null  // Made optional until field is added to database
          slug: string
          owner_id: string | null
          settings: Json
          theme: Json
          is_public: boolean
          max_connections: number
          last_active: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          description?: string | null
          slug?: string
          owner_id?: string | null
          settings?: Json
          theme?: Json
          is_public?: boolean
          max_connections?: number
          last_active?: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          description?: string | null
          slug?: string
          owner_id?: string | null
          settings?: Json
          theme?: Json
          is_public?: boolean
          max_connections?: number
          last_active?: string
        }
      }
      timers: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          room_id: string
          name: string
          description?: string | null
          duration: number
          type: 'countdown' | 'countup' | 'tod'
          status: 'idle' | 'running' | 'paused' | 'finished'
          elapsed_time: number
          started_at: string | null
          trigger: 'manual' | 'linked' | 'scheduled'
          linked_to_timer_id: string | null
          scheduled_start: string | null
          wrap_up_time: number
          overtime_allowed: boolean
          position: number
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          room_id: string
          name?: string
          description?: string | null
          duration?: number
          type?: 'countdown' | 'countup' | 'tod'
          status?: 'idle' | 'running' | 'paused' | 'finished'
          elapsed_time?: number
          started_at?: string | null
          trigger?: 'manual' | 'linked' | 'scheduled'
          linked_to_timer_id?: string | null
          scheduled_start?: string | null
          wrap_up_time?: number
          overtime_allowed?: boolean
          position?: number
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          room_id?: string
          name?: string
          description?: string | null
          duration?: number
          type?: 'countdown' | 'countup' | 'tod'
          status?: 'idle' | 'running' | 'paused' | 'finished'
          elapsed_time?: number
          started_at?: string | null
          trigger?: 'manual' | 'linked' | 'scheduled'
          linked_to_timer_id?: string | null
          scheduled_start?: string | null
          wrap_up_time?: number
          overtime_allowed?: boolean
          position?: number
        }
      }
      messages: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          room_id: string
          text: string
          style: Json
          visible: boolean
          source: 'controller' | 'audience'
          submitter_name: string | null
          position: number
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          room_id: string
          text: string
          style?: Json
          visible?: boolean
          source?: 'controller' | 'audience'
          submitter_name?: string | null
          position?: number
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          room_id?: string
          text?: string
          style?: Json
          visible?: boolean
          source?: 'controller' | 'audience'
          submitter_name?: string | null
          position?: number
        }
      }
      room_connections: {
        Row: {
          id: string
          created_at: string
          room_id: string
          user_id: string | null
          device_id: string
          device_name: string | null
          connection_type: 'controller' | 'viewer' | 'moderator'
          is_active: boolean
          last_ping: string
        }
        Insert: {
          id?: string
          created_at?: string
          room_id: string
          user_id?: string | null
          device_id: string
          device_name?: string | null
          connection_type: 'controller' | 'viewer' | 'moderator'
          is_active?: boolean
          last_ping?: string
        }
        Update: {
          id?: string
          created_at?: string
          room_id?: string
          user_id?: string | null
          device_id?: string
          device_name?: string | null
          connection_type?: 'controller' | 'viewer' | 'moderator'
          is_active?: boolean
          last_ping?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Helper types
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Insertable<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updatable<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Specific table types for easier usage
export type Room = Tables<'rooms'>
export type Timer = Tables<'timers'>
export type Message = Tables<'messages'>
export type RoomConnection = Tables<'room_connections'>
export type Profile = Tables<'profiles'>

// Additional types for better type safety
export interface MessageStyle {
  color: 'white' | 'green' | 'red'
  bold: boolean
  uppercase: boolean
}

export interface RoomTheme {
  primaryColor: string
  secondaryColor: string
}

export interface RoomSettings {
  allowAudienceQuestions?: boolean
  requireAuthentication?: boolean
  enableSound?: boolean
  [key: string]: any
}