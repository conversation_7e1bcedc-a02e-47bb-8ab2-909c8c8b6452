import { createClient } from '@/lib/supabase/client'

export interface DashboardStats {
  totalSessions: number
  activeNow: number
  teamMembers: number
  efficiency: number
}

export async function getDashboardStats(userId: string): Promise<DashboardStats> {
  const supabase = createClient()
  
  try {
    // Get total sessions (rooms) count for the user
    const { count: totalSessions } = await supabase
      .from('rooms')
      .select('*', { count: 'exact', head: true })
      .eq('owner_id', userId)

    // Get active sessions (rooms with recent activity in last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    const { count: activeNow } = await supabase
      .from('rooms')
      .select('*', { count: 'exact', head: true })
      .eq('owner_id', userId)
      .gte('last_active', twentyFourHoursAgo)

    // Get team members (unique users who have connected to user's rooms)
    const { data: connections } = await supabase
      .from('room_connections')
      .select('user_id, room_id')
      .not('user_id', 'is', null)
      .in('room_id', 
        await supabase
          .from('rooms')
          .select('id')
          .eq('owner_id', userId)
          .then(({ data }) => data?.map(r => r.id) || [])
      )
    
    const uniqueTeamMembers = new Set(connections?.map(c => c.user_id) || [])
    const teamMembers = uniqueTeamMembers.size

    // Calculate efficiency (percentage of completed timers)
    const { data: allTimers } = await supabase
      .from('timers')
      .select('status')
      .in('room_id',
        await supabase
          .from('rooms')
          .select('id')
          .eq('owner_id', userId)
          .then(({ data }) => data?.map(r => r.id) || [])
      )

    const completedTimers = allTimers?.filter(t => t.status === 'finished').length || 0
    const totalTimers = allTimers?.length || 0
    const efficiency = totalTimers > 0 ? Math.round((completedTimers / totalTimers) * 100) : 0

    return {
      totalSessions: totalSessions || 0,
      activeNow: activeNow || 0,
      teamMembers,
      efficiency
    }
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return {
      totalSessions: 0,
      activeNow: 0,
      teamMembers: 0,
      efficiency: 0
    }
  }
}

export interface AnalyticsStats {
  sessions: number
  watchTime: string
  accuracy: number
  users: number
}

export async function getAnalyticsStats(userId: string): Promise<AnalyticsStats> {
  const supabase = createClient()
  
  try {
    // Get user's rooms
    const { data: userRooms } = await supabase
      .from('rooms')
      .select('id')
      .eq('owner_id', userId)
    
    const roomIds = userRooms?.map(r => r.id) || []

    // Get total sessions count
    const sessions = roomIds.length

    // Calculate total watch time from all timers
    const { data: timers } = await supabase
      .from('timers')
      .select('duration, elapsed_time')
      .in('room_id', roomIds)

    const totalSeconds = timers?.reduce((acc, timer) => {
      return acc + (timer.elapsed_time || 0)
    }, 0) || 0

    const hours = Math.floor(totalSeconds / 3600)
    const watchTime = `${hours}h`

    // Calculate timing accuracy (how close timers finish to their duration)
    const finishedTimers = await supabase
      .from('timers')
      .select('duration, elapsed_time')
      .in('room_id', roomIds)
      .eq('status', 'finished')
      .then(({ data }) => data || [])

    let accuracySum = 0
    let accuracyCount = 0

    finishedTimers.forEach(timer => {
      if (timer.duration > 0) {
        const accuracyPercent = Math.min(100, Math.abs(1 - Math.abs(timer.elapsed_time - timer.duration) / timer.duration) * 100)
        accuracySum += accuracyPercent
        accuracyCount++
      }
    })

    const accuracy = accuracyCount > 0 ? Math.round(accuracySum / accuracyCount * 10) / 10 : 0

    // Get unique users who have connected to the rooms
    const { data: connections } = await supabase
      .from('room_connections')
      .select('user_id, device_id')
      .in('room_id', roomIds)

    const uniqueUsers = new Set<string>()
    connections?.forEach(conn => {
      if (conn.user_id) uniqueUsers.add(conn.user_id)
      else uniqueUsers.add(conn.device_id)
    })

    const users = uniqueUsers.size

    return {
      sessions,
      watchTime,
      accuracy,
      users
    }
  } catch (error) {
    console.error('Error fetching analytics stats:', error)
    return {
      sessions: 0,
      watchTime: '0h',
      accuracy: 0,
      users: 0
    }
  }
}