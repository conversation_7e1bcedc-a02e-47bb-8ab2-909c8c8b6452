export const TIMER_LIMITS = {
  FREE: 5 * 60, // 5 minutes in seconds
  PRO: Infinity, // No limit
  ENTERPRISE: Infinity, // No limit
} as const

export type PlanType = 'free' | 'pro' | 'enterprise'

export function getTimerLimit(planType: PlanType): number {
  switch (planType) {
    case 'free':
      return TIMER_LIMITS.FREE
    case 'pro':
      return TIMER_LIMITS.PRO
    case 'enterprise':
      return TIMER_LIMITS.ENTERPRISE
    default:
      return TIMER_LIMITS.FREE
  }
}

export function isTimerLimitReached(planType: PlanType, elapsedSeconds: number): boolean {
  const limit = getTimerLimit(planType)
  return limit !== Infinity && elapsedSeconds >= limit
}

export function getRemainingTime(planType: PlanType, elapsedSeconds: number): number {
  const limit = getTimerLimit(planType)
  if (limit === Infinity) return Infinity
  return Math.max(0, limit - elapsedSeconds)
}

export function formatTimeLimit(seconds: number): string {
  if (seconds === Infinity) return 'Unlimited'
  
  const minutes = Math.floor(seconds / 60)
  const secs = seconds % 60
  
  if (minutes === 0) {
    return `${secs}s`
  }
  
  return secs === 0 ? `${minutes}m` : `${minutes}m ${secs}s`
}