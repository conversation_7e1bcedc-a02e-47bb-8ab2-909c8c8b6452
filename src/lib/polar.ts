import { Polar } from '@polar-sh/sdk'

if (!process.env.POLAR_SECRET_KEY) {
  throw new Error('POLAR_SECRET_KEY is required')
}

if (!process.env.NEXT_PUBLIC_POLAR_ORGANIZATION_ID) {
  throw new Error('NEXT_PUBLIC_POLAR_ORGANIZATION_ID is required')
}

export const polar = new Polar({
  accessToken: process.env.POLAR_SECRET_KEY,
  server: process.env.POLAR_ENVIRONMENT === 'sandbox' ? 'sandbox' : 'production',
})

export const POLAR_ORGANIZATION_ID = process.env.NEXT_PUBLIC_POLAR_ORGANIZATION_ID
export const POLAR_ENVIRONMENT = process.env.POLAR_ENVIRONMENT || 'sandbox'
export const POLAR_WEBHOOK_SECRET = process.env.POLAR_WEBHOOK_SECRET

export interface PolarSubscription {
  id: string
  user_id: string
  customer_id: string
  product_id: string
  price_id: string
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid'
  current_period_start: string
  current_period_end: string
  cancel_at_period_end: boolean
  started_at: string
  ended_at?: string
  created_at: string
  modified_at: string
}

export interface PolarProduct {
  id: string
  name: string
  description?: string
  is_recurring: boolean
  is_archived: boolean
  organization_id: string
}

export interface PolarCustomer {
  id: string
  email: string
  name?: string
  organization_id: string
}