import { createClient } from '@/lib/supabase/server'
import { polar, POLAR_ORGANIZATION_ID } from '@/lib/polar'

export interface UserSubscription {
  id: string
  user_id: string
  polar_customer_id: string
  polar_subscription_id: string
  polar_product_id: string
  polar_price_id: string
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid'
  current_period_start: string
  current_period_end: string
  cancel_at_period_end: boolean
  started_at: string
  ended_at?: string
  created_at: string
  updated_at: string
}

export interface UserPlan {
  id: string
  user_id: string
  plan_type: 'free' | 'pro' | 'enterprise'
  is_active: boolean
  subscription_id?: string
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  polar_product_id: string
  name: string
  description?: string
  plan_type: 'pro' | 'enterprise'
  is_recurring: boolean
  is_archived: boolean
  created_at: string
  updated_at: string
}

export class SubscriptionService {
  private supabase = createClient()

  async getUserPlan(userId: string): Promise<UserPlan | null> {
    const { data, error } = await this.supabase
      .from('user_plans')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching user plan:', error)
      return null
    }

    return data
  }

  async getUserSubscriptions(userId: string): Promise<UserSubscription[]> {
    const { data, error } = await this.supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching user subscriptions:', error)
      return []
    }

    return data || []
  }

  async getActiveSubscription(userId: string): Promise<UserSubscription | null> {
    const { data, error } = await this.supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      console.error('Error fetching active subscription:', error)
      return null
    }

    return data
  }

  async syncUserSubscriptions(userId: string, userEmail: string): Promise<void> {
    try {
      // Get or create customer in Polar
      const customers = await polar.customers.list({
        organizationId: POLAR_ORGANIZATION_ID,
        email: userEmail,
      })

      let customerId: string

      if (customers.data?.result && customers.data.result.length > 0) {
        customerId = customers.data.result[0].id
      } else {
        // Create customer if doesn't exist
        const newCustomer = await polar.customers.create({
          organizationId: POLAR_ORGANIZATION_ID,
          email: userEmail,
        })
        customerId = newCustomer.data?.id || ''
      }

      if (!customerId) {
        throw new Error('Failed to get or create customer')
      }

      // Get subscriptions from Polar
      const subscriptions = await polar.subscriptions.list({
        organizationId: POLAR_ORGANIZATION_ID,
        customerId: customerId,
      })

      if (!subscriptions.data?.result) {
        return
      }

      // Sync each subscription
      for (const subscription of subscriptions.data.result) {
        await this.upsertSubscription(userId, customerId, subscription)
      }

      // Update user plan based on active subscriptions
      await this.updateUserPlan(userId)

    } catch (error) {
      console.error('Error syncing user subscriptions:', error)
      throw error
    }
  }

  private async upsertSubscription(userId: string, customerId: string, subscription: any): Promise<void> {
    const subscriptionData = {
      user_id: userId,
      polar_customer_id: customerId,
      polar_subscription_id: subscription.id,
      polar_product_id: subscription.product_id,
      polar_price_id: subscription.price_id,
      status: subscription.status,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end || false,
      started_at: subscription.started_at,
      ended_at: subscription.ended_at || null,
    }

    const { error } = await this.supabase
      .from('user_subscriptions')
      .upsert(subscriptionData, {
        onConflict: 'polar_subscription_id'
      })

    if (error) {
      console.error('Error upserting subscription:', error)
      throw error
    }
  }

  private async updateUserPlan(userId: string): Promise<void> {
    // Get active subscription to determine plan type
    const activeSubscription = await this.getActiveSubscription(userId)
    
    let planType: 'free' | 'pro' | 'enterprise' = 'free'
    let subscriptionId: string | null = null

    if (activeSubscription) {
      // Get product to determine plan type
      const { data: product } = await this.supabase
        .from('products')
        .select('plan_type')
        .eq('polar_product_id', activeSubscription.polar_product_id)
        .single()

      if (product) {
        planType = product.plan_type
        subscriptionId = activeSubscription.id
      }
    }

    // Update user plan
    const { error } = await this.supabase
      .from('user_plans')
      .upsert({
        user_id: userId,
        plan_type: planType,
        is_active: true,
        subscription_id: subscriptionId,
      }, {
        onConflict: 'user_id'
      })

    if (error) {
      console.error('Error updating user plan:', error)
      throw error
    }
  }

  async createCheckoutSession(userId: string, productId: string, successUrl: string, cancelUrl: string) {
    try {
      const checkoutSession = await polar.checkouts.create({
        productId: productId,
        successUrl: successUrl,
        cancelUrl: cancelUrl,
        metadata: {
          userId: userId,
        },
      })

      return checkoutSession.data
    } catch (error) {
      console.error('Error creating checkout session:', error)
      throw error
    }
  }

  async getProducts(): Promise<Product[]> {
    const { data, error } = await this.supabase
      .from('products')
      .select('*')
      .eq('is_archived', false)
      .order('plan_type')

    if (error) {
      console.error('Error fetching products:', error)
      return []
    }

    return data || []
  }

  async syncProducts(): Promise<void> {
    try {
      const products = await polar.products.list({
        organizationId: POLAR_ORGANIZATION_ID,
      })

      if (!products.data?.result) {
        return
      }

      for (const product of products.data.result) {
        // Determine plan type based on product name or other criteria
        let planType: 'pro' | 'enterprise' = 'pro'
        if (product.name.toLowerCase().includes('enterprise')) {
          planType = 'enterprise'
        }

        const productData = {
          polar_product_id: product.id,
          name: product.name,
          description: product.description || null,
          plan_type: planType,
          is_recurring: product.is_recurring || false,
          is_archived: product.is_archived || false,
        }

        const { error } = await this.supabase
          .from('products')
          .upsert(productData, {
            onConflict: 'polar_product_id'
          })

        if (error) {
          console.error('Error upserting product:', error)
        }
      }
    } catch (error) {
      console.error('Error syncing products:', error)
      throw error
    }
  }
}

export const subscriptionService = new SubscriptionService()