-- Create rooms table
CREATE TABLE IF NOT EXISTS public.rooms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Room details
  name VARCHAR(255) NOT NULL DEFAULT 'Untitled Room',
  slug VARCHAR(50) UNIQUE NOT NULL,
  
  -- Owner (null for anonymous rooms)
  owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Settings
  settings JSONB DEFAULT '{}',
  theme JSONB DEFAULT '{"primaryColor": "#00DC82", "secondaryColor": "#FF0080"}',
  
  -- Access control
  is_public BOOLEAN DEFAULT true,
  max_connections INTEGER DEFAULT 100,
  
  -- Metadata
  last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create timers table
CREATE TABLE IF NOT EXISTS public.timers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relations
  room_id UUID NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
  
  -- Timer details
  name VARCHAR(255) NOT NULL DEFAULT 'Timer',
  duration INTEGER NOT NULL DEFAULT 300, -- in seconds
  type VARCHAR(20) NOT NULL DEFAULT 'countdown' CHECK (type IN ('countdown', 'countup', 'tod')),
  
  -- State
  status VARCHAR(20) NOT NULL DEFAULT 'idle' CHECK (status IN ('idle', 'running', 'paused', 'finished')),
  elapsed_time INTEGER DEFAULT 0, -- in seconds
  started_at TIMESTAMP WITH TIME ZONE,
  
  -- Settings
  trigger VARCHAR(20) DEFAULT 'manual' CHECK (trigger IN ('manual', 'linked', 'scheduled')),
  linked_to_timer_id UUID REFERENCES public.timers(id) ON DELETE SET NULL,
  scheduled_start TIMESTAMP WITH TIME ZONE,
  
  -- Display settings
  wrap_up_time INTEGER DEFAULT 60, -- seconds before end to show warning
  overtime_allowed BOOLEAN DEFAULT true,
  
  -- Order
  position INTEGER NOT NULL DEFAULT 0
);

-- Create messages table
CREATE TABLE IF NOT EXISTS public.messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relations
  room_id UUID NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
  
  -- Message details
  text TEXT NOT NULL,
  style JSONB DEFAULT '{"color": "white", "bold": false, "uppercase": false}',
  visible BOOLEAN DEFAULT false,
  
  -- Source
  source VARCHAR(20) DEFAULT 'controller' CHECK (source IN ('controller', 'audience')),
  submitter_name VARCHAR(100),
  
  -- Order
  position INTEGER NOT NULL DEFAULT 0
);

-- Create room_connections table for tracking active connections
CREATE TABLE IF NOT EXISTS public.room_connections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relations
  room_id UUID NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Connection details
  device_id VARCHAR(10) NOT NULL,
  device_name VARCHAR(100),
  connection_type VARCHAR(20) NOT NULL CHECK (connection_type IN ('controller', 'viewer', 'moderator')),
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  last_ping TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint for device per room
  UNIQUE(room_id, device_id)
);

-- Create indexes
CREATE INDEX idx_rooms_slug ON public.rooms(slug);
CREATE INDEX idx_rooms_owner ON public.rooms(owner_id);
CREATE INDEX idx_timers_room ON public.timers(room_id);
CREATE INDEX idx_timers_position ON public.timers(room_id, position);
CREATE INDEX idx_messages_room ON public.messages(room_id);
CREATE INDEX idx_messages_position ON public.messages(room_id, position);
CREATE INDEX idx_connections_room ON public.room_connections(room_id);
CREATE INDEX idx_connections_active ON public.room_connections(room_id, is_active);

-- Enable RLS
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.timers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_connections ENABLE ROW LEVEL SECURITY;

-- RLS Policies for rooms
CREATE POLICY "Public rooms are viewable by everyone" ON public.rooms
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view their own rooms" ON public.rooms
  FOR SELECT USING (auth.uid() = owner_id);

CREATE POLICY "Users can create rooms" ON public.rooms
  FOR INSERT WITH CHECK (auth.uid() = owner_id OR owner_id IS NULL);

CREATE POLICY "Users can update their own rooms" ON public.rooms
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own rooms" ON public.rooms
  FOR DELETE USING (auth.uid() = owner_id);

-- RLS Policies for timers (inherit room access)
CREATE POLICY "Timers viewable based on room access" ON public.timers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.rooms 
      WHERE rooms.id = timers.room_id 
      AND (rooms.is_public = true OR rooms.owner_id = auth.uid())
    )
  );

CREATE POLICY "Timers manageable by room owner" ON public.timers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.rooms 
      WHERE rooms.id = timers.room_id 
      AND rooms.owner_id = auth.uid()
    )
  );

-- RLS Policies for messages (inherit room access)
CREATE POLICY "Messages viewable based on room access" ON public.messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.rooms 
      WHERE rooms.id = messages.room_id 
      AND (rooms.is_public = true OR rooms.owner_id = auth.uid())
    )
  );

CREATE POLICY "Messages manageable by room owner" ON public.messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.rooms 
      WHERE rooms.id = messages.room_id 
      AND rooms.owner_id = auth.uid()
    )
  );

-- RLS Policies for connections
CREATE POLICY "Connections viewable based on room access" ON public.room_connections
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.rooms 
      WHERE rooms.id = room_connections.room_id 
      AND (rooms.is_public = true OR rooms.owner_id = auth.uid())
    )
  );

CREATE POLICY "Anyone can create connections to public rooms" ON public.room_connections
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.rooms 
      WHERE rooms.id = room_connections.room_id 
      AND rooms.is_public = true
    )
  );

CREATE POLICY "Users can update their own connections" ON public.room_connections
  FOR UPDATE USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can delete their own connections" ON public.room_connections
  FOR DELETE USING (auth.uid() = user_id OR user_id IS NULL);

-- Functions
CREATE OR REPLACE FUNCTION generate_room_slug()
RETURNS TEXT AS $$
DECLARE
  chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  result TEXT := '';
  i INTEGER;
BEGIN
  FOR i IN 1..8 LOOP
    result := result || substr(chars, floor(random() * length(chars) + 1)::int, 1);
  END LOOP;
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate slug for new rooms
CREATE OR REPLACE FUNCTION set_room_slug()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.slug IS NULL THEN
    LOOP
      NEW.slug := generate_room_slug();
      EXIT WHEN NOT EXISTS (SELECT 1 FROM public.rooms WHERE slug = NEW.slug);
    END LOOP;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_room_slug_trigger
  BEFORE INSERT ON public.rooms
  FOR EACH ROW
  EXECUTE FUNCTION set_room_slug();

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_rooms_updated_at
  BEFORE UPDATE ON public.rooms
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_timers_updated_at
  BEFORE UPDATE ON public.timers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_messages_updated_at
  BEFORE UPDATE ON public.messages
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();